@font-face {
  font-family: 'icomoon';
  src:  url('icomoon95b7.eot?tey8qv');
  src:  url('icomoon95b7.eot?tey8qv#iefix') format('embedded-opentype'),
    url('icomoon95b7.ttf?tey8qv') format('truetype'),
    url('icomoon95b7.woff?tey8qv') format('woff'),
    url('icomoon95b7.svg?tey8qv#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-in:before {
  content: "\e93d";
  color: #4d4e4f;
}
.icon-search2:before {
  content: "\e93c";
}
.icon-instagram2:before {
  content: "\e93e";
}
.icon-eye-hide-line:before {
  content: "\e900";
}
.icon-comment:before {
  content: "\e901";
}
.icon-calendar:before {
  content: "\e902";
}
.icon-user:before {
  content: "\e903";
}
.icon-modave:before {
  content: "\e904";
}
.icon-map-pin:before {
  content: "\e905";
}
.icon-arrowClockwise:before {
  content: "\e906";
}
.icon-timer:before {
  content: "\e907";
}
.icon-share:before {
  content: "\e908";
}
.icon-question:before {
  content: "\e909";
}
.icon-checkCircle:before {
  content: "\e90a";
}
.icon-filter:before {
  content: "\e90b";
}
.icon-pharmacy:before {
  content: "\e90c";
}
.icon-cheese:before {
  content: "\e90d";
}
.icon-bird:before {
  content: "\e90e";
}
.icon-bone:before {
  content: "\e90f";
}
.icon-cat:before {
  content: "\e910";
}
.icon-dog:before {
  content: "\e911";
}
.icon-fish:before {
  content: "\e912";
}
.icon-sm-pet:before {
  content: "\e913";
}
.icon-categories:before {
  content: "\e914";
}
.icon-security:before {
  content: "\e915";
}
.icon-audio:before {
  content: "\e916";
}
.icon-headphones:before {
  content: "\e917";
}
.icon-software:before {
  content: "\e918";
}
.icon-tv:before {
  content: "\e919";
}
.icon-smartphone:before {
  content: "\e91a";
}
.icon-camera:before {
  content: "\e91b";
}
.icon-laptop:before {
  content: "\e91c";
}
.icon-squares-four:before {
  content: "\e91d";
}
.icon-tag:before {
  content: "\e91e";
}
.icon-lifebuoy:before {
  content: "\e91f";
}
.icon-ShoppingBagOpen:before {
  content: "\e920";
}
.icon-pants:before {
  content: "\e921";
}
.icon-tshirt:before {
  content: "\e922";
}
.icon-close:before {
  content: "\e923";
}
.icon-quote:before {
  content: "\e924";
}
.icon-lightning-line:before {
  content: "\e925";
}
.icon-sealCheck:before {
  content: "\e926";
}
.icon-headset:before {
  content: "\e927";
}
.icon-shipping:before {
  content: "\e928";
}
.icon-return:before {
  content: "\e929";
}
.icon-star:before {
  content: "\e92a";
}
.icon-mail:before {
  content: "\e92b";
}
.icon-phone:before {
  content: "\e92c";
}
.icon-pinterest:before {
  content: "\e92d";
}
.icon-amazon:before {
  content: "\e92e";
}
.icon-fb:before {
  content: "\e92f";
}
.icon-tiktok:before {
  content: "\e930";
}
.icon-x:before {
  content: "\e931";
}
.icon-whatsapp:before {
  content: "\ea93";
}
.icon-youtube:before {
  content: "\ea9d";
}
.icon-instagram:before {
  content: "\e932";
}
.icon-arrLeft:before {
  content: "\e933";
}
.icon-arrRight:before {
  content: "\e934";
}
.icon-arrow-down:before {
  content: "\e935";
}
.icon-arrowUpRight:before {
  content: "\e936";
}
.icon-check:before {
  content: "\e937";
}
.icon-eye:before {
  content: "\e938";
}
.icon-gitDiff:before {
  content: "\e939";
}
.icon-heart:before {
  content: "\e93a";
}
.icon-lightning:before {
  content: "\e93b";
}
