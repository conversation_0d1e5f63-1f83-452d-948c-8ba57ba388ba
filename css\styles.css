@charset "UTF-8";
/* ---------------------------------------------------------
    * Name: Modave - Multipurpose eCommerce
    * Version: 1.0.2
    * Author: Themesflat
    * Author URI: http://themesflat.com 

	* Abstracts variable

    * Reset css styles

    * Components

        * header
        * footer
        * tabs
        * slider banner
        * button
        * form
        * nice select
        * carousel
        * avatar
        * pop up
        * box icon
        * hover
        * collection
        * product
        * blog
        * testimonial
        * lookbook
        * accordion
        * zoom
        * shop

    * section

    * Responsive
 ------------------------------------------------------------------------------ */
/*--------- Abstracts variable ---------- */
@import url(bootstrap-select.min.css);
:root {
  --primary: #e43131;
  --main: #181818;
  --secondary: #4d4e4f;
  --secondary-2: #a0a0a0;
  --white: #ffffff;
  --surface: #f7f7f7;
  --critical: #f03e3e;
  --warning: #9391e1;
  --success: #3dab25;
  --yellow: #f0a750;
  --line: #e9e9e9;
  --pink: #ec749d;
  --blue: #0c74d6;
  --main-rgba-1: rgba(0, 0, 0, 0.16);
  --main-rgba-2: rgba(0, 0, 0, 0.15);
  --gradient: linear-gradient(87deg, #fbf1f1 3.59%, #f4f1fa 95.02%);
  --rgba-primary: rgba(228, 49, 49, 0.1);
  --bg-scrollbar-track: #f1f1f1;
  --bg-scrollbar-thumb: #c1c1c1;
  --shadow1: 0px 10px 25px 0px #2b344a1f;
  --shadow2: 0px 5px 18px 5px #40485726;
  --backdrop: rgba(24, 24, 24, 0.2);
}

/*---------- Reset css styles ----------- */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  font-family: inherit;
  font-size: 100%;
  font-style: inherit;
  font-weight: inherit;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

/* Elements
-------------------------------------------------------------- */
html {
  margin-right: 0 !important;
  scroll-behavior: smooth;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Kumbh Sans", sans-serif;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: var(--main);
  background-color: var(--white);
}

img {
  max-width: 100%;
  height: auto;
  transform: scale(1);
  vertical-align: middle;
  -ms-interpolation-mode: bicubic;
}

.row {
  margin-right: -15px;
  margin-left: -15px;
}
.row > * {
  padding-left: 15px;
  padding-right: 15px;
}

ul,
li {
  list-style-type: none;
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}

.container {
  max-width: 1320px;
}

.container {
  width: 100%;
  margin: auto;
}

.container {
  padding-left: 15px;
  padding-right: 15px;
}

.container-full4,
.container-full3,
.container-full2,
.container-full {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0px 15px;
}

.container-full3 {
  padding: 0px 14px;
}

.container-full4 {
  padding: 0px 20px;
}

.slider-layout-right {
  width: calc(100vw - (100vw - 1290px) / 2);
  margin-right: unset;
  max-width: 100%;
  margin-left: auto;
}
.slider-layout-right .swiper {
  margin-right: -15px;
}

svg path {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

textarea,
input[type=text],
input[type=password],
input[type=datetime],
input[type=datetime-local],
input[type=date],
input[type=month],
input[type=time],
input[type=week],
input[type=number],
input[type=email],
input[type=url],
input[type=search],
input[type=tel],
input[type=color] {
  border: 2px solid var(--line);
  outline: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  font-size: 16px;
  line-height: 26px;
  border-radius: 8px;
  padding: 9px 16px;
  width: 100%;
  background: var(--white);
  color: var(--main);
  font-weight: 400;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
textarea:hover, textarea:focus,
input[type=text]:hover,
input[type=text]:focus,
input[type=password]:hover,
input[type=password]:focus,
input[type=datetime]:hover,
input[type=datetime]:focus,
input[type=datetime-local]:hover,
input[type=datetime-local]:focus,
input[type=date]:hover,
input[type=date]:focus,
input[type=month]:hover,
input[type=month]:focus,
input[type=time]:hover,
input[type=time]:focus,
input[type=week]:hover,
input[type=week]:focus,
input[type=number]:hover,
input[type=number]:focus,
input[type=email]:hover,
input[type=email]:focus,
input[type=url]:hover,
input[type=url]:focus,
input[type=search]:hover,
input[type=search]:focus,
input[type=tel]:hover,
input[type=tel]:focus,
input[type=color]:hover,
input[type=color]:focus {
  border-color: var(--main);
}

textarea::placeholder,
input[type=text]::placeholder,
input[type=password]::placeholder,
input[type=datetime]::placeholder,
input[type=datetime-local]::placeholder,
input[type=date]::placeholder,
input[type=month]::placeholder,
input[type=time]::placeholder,
input[type=week]::placeholder,
input[type=number]::placeholder,
input[type=email]::placeholder,
input[type=url]::placeholder,
input[type=search]::placeholder,
input[type=tel]::placeholder,
input[type=color]::placeholder {
  color: var(--secondary-2);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

textarea {
  height: 160px;
  resize: none;
}

/* Placeholder color */
::-webkit-input-placeholder {
  color: var(--secondary-2);
}

:-moz-placeholder {
  color: var(--secondary-2);
}

::-moz-placeholder {
  color: var(--secondary-2);
  opacity: 1;
}

button {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  background-color: var(--main);
  color: var(--white);
  padding: 15px 32px;
  border-radius: 99px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  text-transform: capitalize;
}
button .icon {
  font-size: 22px;
}
button:hover {
  background-color: transparent;
  color: var(--main);
}

/* Since FF19 lowers the opacity of the placeholder by default */
:-ms-input-placeholder {
  color: var(--secondary-2);
}

/* Typography
-------------------------------------------------------------- */
.title-display,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Kumbh Sans", sans-serif;
  text-rendering: optimizeLegibility;
  color: var(--main);
  font-weight: 500;
}

.title-display {
  font-size: 80px;
  line-height: 88px;
}

.title-display-2 {
  font-size: 140px;
  line-height: 160px;
}

h1 {
  font-size: 56px;
  line-height: 68px;
}

h2 {
  font-size: 44px;
  line-height: 50px;
}

h3 {
  font-size: 40px;
  line-height: 48px;
}

h4 {
  font-size: 30px;
  line-height: 42px;
}

h5 {
  font-size: 24px;
  line-height: 30px;
}

h6 {
  font-size: 20px;
  line-height: 28px;
}

.h6 {
  font-size: 20px !important;
  line-height: 28px !important;
  margin-bottom: 0;
}

.body-text-1 {
  font-size: 18px;
  line-height: 28px;
}

.body-text {
  font-size: 16px;
  line-height: 26px;
}

.text-title {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}

.text-button {
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
}

.text-btn-uppercase {
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
}

.text-btn-uppercase2 {
  font-family: "Instrument Sans", sans-serif;
  font-size: 18px;
  line-height: 24px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
}

.text-caption-1 {
  font-size: 14px;
  line-height: 22px;
}

.text-caption-2 {
  font-size: 12px;
  line-height: 16px;
}

.font-main {
  font-family: "Kumbh Sans", sans-serif;
}

.font-2 {
  font-family: "Instrument Sans", sans-serif !important;
}

.font-3 {
  font-family: "Plus Jakarta Sans", sans-serif !important;
}

.font-4 {
  font-family: "Barlow Condensed", serif !important;
}

.font-5 {
  font-family: "Inter Tight", serif !important;
}

b,
strong {
  font-weight: bolder;
}

video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.fw-4 {
  font-weight: 400 !important;
}

.fw-5 {
  font-weight: 500 !important;
}

.fw-6 {
  font-weight: 600 !important;
}

.fw-7 {
  font-weight: 700 !important;
}

.fw-8 {
  font-weight: 800 !important;
}

.text-primary {
  color: var(--primary) !important;
}

.text-main {
  color: var(--main) !important;
}

.text-secondary {
  color: var(--secondary) !important;
}

.text-secondary-2 {
  color: var(--secondary-2) !important;
}

.text-white {
  color: var(--white) !important;
}

.text-surface {
  color: var(--surface) !important;
}

.text-critical {
  color: var(--critical) !important;
}

.text-warning {
  color: var(--warning) !important;
}

.text-success {
  color: var(--success) !important;
}

.text-yellow {
  color: var(--yellow) !important;
}

.text-line {
  color: var(--line) !important;
}

.text-pink {
  color: var(--pink) !important;
}

.text-blue {
  color: var(--blue) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
}

.bg-main {
  background-color: var(--main) !important;
}

.bg-secondary {
  background-color: var(--secondary) !important;
}

.bg-secondary-2 {
  background-color: var(--secondary-2) !important;
}

.bg-white {
  background-color: var(--white) !important;
}

.bg-surface {
  background-color: var(--surface) !important;
}

.bg-critical {
  background-color: var(--critical) !important;
}

.bg-warning {
  background-color: var(--warning) !important;
}

.bg-success {
  background-color: var(--success) !important;
}

.bg-yellow {
  background-color: var(--yellow) !important;
}

.bg-line {
  background-color: var(--line) !important;
}

.bg-pink {
  background-color: var(--pink) !important;
}

.bg-light-pink {
  background-color: #dfc6b8 !important;
}

.bg-light-pink-2 {
  background-color: #f4c5bf !important;
}

.bg-light-pink-3 {
  background-color: #fdebeb !important;
}

.bg-dark-pink {
  background-color: #d10047 !important;
}

.bg-blue {
  background-color: var(--blue) !important;
}

.bg-blue-2 {
  background-color: #0c74d6 !important;
}

.bg-blue-3 {
  background-color: #0766bf !important;
}

.bg-blue-4 {
  background-color: #439699 !important;
}

.bg-light-blue {
  background-color: #d3d9ef !important;
}

.bg-light-blue-2 {
  background-color: #ebeaef !important;
}

.bg-light-blue-3 {
  background-color: #edf5f6 !important;
}

.bg-light-blue-4 {
  background-color: #eaf5ff !important;
}

.bg-light-blue-5 {
  background-color: #c9dfed !important;
}

.bg-light-blue-6 {
  background-color: #d9f0ea !important;
}

.bg-dark-blue {
  background-color: #1b4872 !important;
}

.bg-purple {
  background-color: #2d3054 !important;
}

.bg-purple-2 {
  background-color: #6461be !important;
}

.bg-purple-3 {
  background-color: #73465a !important;
}

.bg-orange {
  background-color: #ff9747 !important;
}

.bg-orange-2 {
  background-color: #f1592a !important;
}

.bg-light-orange {
  background-color: #eeae76 !important;
}

.bg-grey {
  background-color: #e0d3c5 !important;
}

.bg-grey-2 {
  background-color: #d7cfc4 !important;
}

.bg-dark-grey {
  background-color: #b1aa98 !important;
}

.bg-dark-grey-2 {
  background-color: #9e9b96 !important;
}

.bg-light-grey {
  background-color: #f0efed !important;
}

.bg-light-green {
  background-color: #b7b8a3 !important;
}

.bg-light-green-2 {
  background-color: #a9ef78 !important;
}

.bg-brown {
  background-color: #d6bb9a !important;
}

.bg-brown-2 {
  background-color: #f6ede6 !important;
}

.bg-brown-3 {
  background-color: #f7f5f0 !important;
}

.bg-red {
  background-color: #dc2a35 !important;
}

.bg-red-2 {
  background-color: #d14244 !important;
}

.bg-beige {
  background-color: #ccbba7 !important;
}

.bg-beige-2 {
  background-color: #f6efdd !important;
}

.bg-beige-1 {
  background-color: #f6f1ee !important;
}

.bg_F5F0EC {
  background-color: #f5f0ec !important;
}

.bg_A89A8C {
  background-color: #a89a8c !important;
}

.bg_F4F4F5 {
  background-color: #f4f4f5 !important;
}

.letter-0 {
  letter-spacing: 0px !important;
}

.letter-1 {
  letter-spacing: 0.1em;
}

.letter-2 {
  letter-spacing: 0.2em;
}

.text-stroke-white {
  -webkit-text-stroke: 2px var(--white);
  color: transparent;
}

a {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  text-decoration: none;
  cursor: pointer;
  display: inline-block;
  color: var(--main);
}
a:focus, a:hover {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  text-decoration: none;
  outline: 0;
}

.link {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.link:hover {
  color: var(--primary) !important;
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}

.grid-6 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
}

.gap-4 {
  gap: 4px !important;
}

.gap-5 {
  gap: 5px !important;
}

.gap-6 {
  gap: 6px !important;
}

.gap-8 {
  gap: 8px;
}

.gap-10 {
  gap: 10px;
}

.gap-12 {
  gap: 12px !important;
}

.gap-15 {
  gap: 15px;
}

.gap-16 {
  gap: 16px;
}

.gap-20 {
  gap: 20px;
}

.gap-24 {
  gap: 24px !important;
}

.row-gap-30 {
  row-gap: 30px !important;
}

.line {
  border: 1px solid var(--line);
}

.line-2 {
  border: 1px solid rgba(255, 255, 255, 0.1019607843) !important;
}

.line-bt {
  border-bottom: 1px solid var(--line);
}

.line-top {
  border-top: 1px solid var(--line);
}

.line-top-rgba {
  border-top: 1px solid rgba(233, 233, 233, 0.1);
}

.line-black {
  border: 1px solid var(--main);
}

.line-right {
  border-right: 1px solid rgba(24, 24, 24, 0.1);
}

.line-secondary-2 {
  border: 1px solid var(--secondary-2) !important;
}

.no-line {
  border: 0 !important;
}

.place-self-center {
  place-self: center !important;
}

.radius-3 {
  border-radius: 3px !important;
}

.radius-5 {
  border-radius: 5px !important;
}

.radius-10 {
  border-radius: 10px !important;
}

.radius-12 {
  border-radius: 12px !important;
}

.radius-16 {
  border-radius: 16px !important;
}

.radius-20 {
  border-radius: 20px !important;
}

.radius-60 {
  border-radius: 60px !important;
}

.rounded-full {
  border-radius: 999px !important;
}

.o-hidden {
  overflow: hidden;
}

.h-40 {
  height: 40px;
}

.h-46 {
  height: 46px;
}

.h-52 {
  height: 52px;
}

.px_15 {
  padding-left: 15px;
  padding-right: 15px;
}

.py-4 {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

.box-center {
  position: absolute;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.pt_0 {
  padding-top: 0px !important;
}

.pt_20 {
  padding-top: 20px;
}

.pb_0 {
  padding-bottom: 0px !important;
}

.pr_0 {
  padding-right: 0px !important;
}

.py_8 {
  padding: 8px 0 !important;
}

.py_15 {
  padding: 15px 0;
}

.py_20 {
  padding: 20px 0;
}

.py_23 {
  padding: 23px 0 !important;
}

.pb_8 {
  padding-bottom: 8px;
}

.pb_15 {
  padding-bottom: 15px;
}

.pb_20 {
  padding-bottom: 20px;
}

.my_20 {
  margin: 20px 0px;
}

.mt_5 {
  margin-top: 5px;
}

.mt_3 {
  margin-top: 3px;
}

.mt_4 {
  margin-top: 4px;
}

.mt_8 {
  margin-top: 8px;
}

.mt_20 {
  margin-top: 20px;
}

.mt_37 {
  margin-top: 37px;
}

.mt_140 {
  margin-top: 140px;
}

.mb_4 {
  margin-bottom: 4px;
}

.mb_8 {
  margin-bottom: 8px;
}

.mb_6 {
  margin-bottom: 6px;
}

.mb_10 {
  margin-bottom: 10px;
}

.mb_12 {
  margin-bottom: 12px;
}

.mb_15 {
  margin-bottom: 15px;
}

.mb_16 {
  margin-bottom: 16px;
}

.mb_18 {
  margin-bottom: 18px;
}

.mb_20 {
  margin-bottom: 20px;
}

.mb_24 {
  margin-bottom: 24px !important;
}

.mb_30 {
  margin-bottom: 30px;
}

.mb_40 {
  margin-bottom: 40px;
}

.mb_32 {
  margin-bottom: 32px;
}

.mb_36 {
  margin-bottom: 36px;
}

.mb_60 {
  margin-bottom: 60px;
}

.mb_200 {
  margin-bottom: 200px;
}

.flat-spacing {
  padding-top: 80px;
  padding-bottom: 80px;
}

.flat-spacing-2 {
  padding-top: 65px;
  padding-bottom: 65px;
}

.flat-spacing-3 {
  padding-top: 80px;
  padding-bottom: 90px;
}

.flat-spacing-4 {
  padding-top: 60px;
  padding-bottom: 60px;
}

.flat-spacing-5 {
  padding: 40px 60px;
}

.flat-spacing-6 {
  padding-top: 90px;
  padding-bottom: 80px;
}

.flat-spacing-7 {
  padding-top: 60px;
  padding-bottom: 80px;
}

.flat-spacing-8 {
  padding-top: 100.5px;
  padding-bottom: 100.5px;
}

.flat-spacing-9 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.flat-spacing-10 {
  padding-top: 175px;
  padding-bottom: 175px;
}

.space-30 {
  padding-top: 30px;
  padding-left: 30px;
  padding-right: 30px;
}

[data-grid=grid-1] {
  display: grid;
  gap: 30px;
  grid-template-columns: 1fr;
}

[data-grid=grid-2] {
  display: grid;
  gap: 30px;
  grid-template-columns: 1fr 1fr;
}

[data-grid=grid-3] {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(3, 1fr);
}

[data-grid=grid-4] {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(4, 1fr);
}

[data-grid=grid-5] {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(5, 1fr);
}

[data-grid=grid-6] {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(6, 1fr);
}

[data-grid=grid-7] {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(7, 1fr);
}

.grid-template-columns-2 {
  grid-template-columns: 1fr 1fr;
}

.tf-row-flex {
  display: flex;
  flex-direction: row;
  column-gap: 30px;
  row-gap: 30px;
}

.tf-grid-layout {
  display: grid;
  column-gap: 15px;
  row-gap: 30px;
}
.tf-grid-layout.tf-col-2 {
  grid-template-columns: 1fr 1fr;
}
.tf-grid-layout.tf-col-3 {
  grid-template-columns: repeat(3, 1fr);
}
.tf-grid-layout.tf-col-4 {
  grid-template-columns: repeat(4, 1fr);
}
.tf-grid-layout.tf-col-5 {
  grid-template-columns: repeat(5, 1fr);
}
.tf-grid-layout.tf-col-6 {
  grid-template-columns: repeat(6, 1fr);
}
.tf-grid-layout.tf-col-7 {
  grid-template-columns: repeat(7, 1fr);
}
.tf-grid-layout .wg-pagination {
  grid-column: 1/-1;
  width: 100%;
}
.tf-grid-layout .wd-load {
  grid-column: 1/-1;
}

.tf-grid-layout-v2 {
  display: grid;
  gap: 15px;
}

.overflow-unset {
  overflow: unset !important;
}

.sticky-top {
  z-index: 50;
  top: 15px;
}

.wmax {
  width: max-content !important;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-auto {
  cursor: auto;
}

.tag-list {
  list-style: disc;
  padding-left: 20px;
}
.tag-list li {
  list-style: inherit;
}

.has-line-bottom {
  position: relative;
}
.has-line-bottom::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 50%;
  background-color: var(--line);
  height: 1px;
  width: 100%;
  max-width: 1440px;
  transform: translateX(-50%);
}

.line-under {
  color: rgba(0, 0, 0, 0.85);
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  text-decoration-line: underline;
  transition: text-decoration-thickness 1s ease;
}
.line-under:hover {
  color: var(--main);
  text-decoration-thickness: 2px;
  text-decoration-line: underline;
}

.transition-linear {
  transition-timing-function: linear !important;
}

.z-5 {
  z-index: 5;
}

.text-highlight {
  -webkit-text-stroke: 1px #000;
  color: transparent !important;
  flex-direction: row-reverse;
}

.text-line-clamp-1 {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.text-line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.aspect-ratio-0 {
  aspect-ratio: 0 !important;
}

.aspect-ratio-1 {
  aspect-ratio: 1/1 !important;
}

.initial-child-container {
  flex: 0 0 auto;
  display: flex;
  min-width: auto;
  flex-direction: row;
  align-items: center;
}

.line-top-container {
  position: relative;
}
.line-top-container::before {
  position: absolute;
  content: "";
  top: 0;
  left: 50%;
  background-color: var(--line);
  height: 1px;
  width: 100%;
  max-width: 1320px;
  transform: translateX(-50%);
}

.line-bottom-container {
  position: relative;
}
.line-bottom-container::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 50%;
  background-color: var(--line);
  height: 1px;
  width: 100%;
  max-width: 1320px;
  transform: translateX(-50%);
}

#scroll-top {
  position: fixed;
  display: block;
  width: 48px;
  height: 48px;
  line-height: 50px;
  border-radius: 4px;
  z-index: 1;
  border-radius: 50%;
  opacity: 0;
  visibility: hidden;
  cursor: pointer;
  overflow: hidden;
  z-index: 100;
  background-color: var(--main);
  border: 0;
  bottom: 92px;
  right: 20px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
#scroll-top.show {
  opacity: 1;
  visibility: visible;
}
#scroll-top.type-1 {
  bottom: 140px;
}
#scroll-top:hover {
  transform: translateY(-5px);
  background-color: var(--primary);
}

/* Preload 
------------------------------------------- */
.preload-wrapper .preload-container {
  display: flex;
}

.preload-container {
  display: none;
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 99999999999;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 3px solid transparent;
  border-top: 3px solid var(--line);
  border-radius: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.br-line {
  width: 100%;
  height: 1px;
}
.br-line.type-vertical {
  height: 24px;
  width: 1px;
}

.opacity-10 {
  opacity: 0.1 !important;
}

.simpleParallax {
  height: 100%;
  width: 100%;
}
.simpleParallax img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.pb-10 {
  padding-bottom: 10px;
}

.text-clip {
  background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.5) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.container-2 {
  max-width: 1760px;
}

.container-3 {
  max-width: 1440px;
}

.justify-items-center {
  justify-items: center;
}

.mt--75 {
  margin-top: -75px;
}

/*------------ Components ---------------- */
/*------------ header ---------------- */
.tf-topbar {
  padding: 12px 0px;
}
.tf-topbar .topbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.tf-topbar.has-line-bot {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.tf-topbar.style-2 {
  padding: 8px 0px;
}
.tf-topbar.topbar-white .top-bar-text {
  color: var(--white);
}
.tf-topbar.topbar-white .navigation-topbar .icon {
  color: var(--white);
}
.tf-topbar.topbar-fullwidth, .tf-topbar.topbar-fullwidth-2 {
  padding-left: 15px;
  padding-right: 15px;
}
.tf-topbar .wrapper-slider-topbar {
  position: relative;
}
.tf-topbar .wrapper-slider-topbar .tf-sw-top_bar {
  margin-left: 40px;
  margin-right: 40px;
}
.tf-topbar .navigation-topbar {
  position: absolute;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  transform: translateY(-50%);
}
.tf-topbar .navigation-topbar .icon {
  font-size: 18px;
}
.tf-topbar .navigation-topbar.nav-next-topbar {
  left: 0;
}
.tf-topbar .navigation-topbar.nav-prev-topbar {
  right: 0;
}

.header-default .wrapper-header {
  min-height: 64px;
}
.header-default .wrapper-header .nav-icon {
  gap: 16px;
}
.header-default .box-nav-ul {
  gap: 20px;
}
.header-default .wrapper-header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
}
.header-default .wrapper-header-right .tf-currencies {
  padding-right: 20px;
  border-right: 1px solid var(--line);
}
.header-default .header-bottom .wrapper-header {
  min-height: 58px;
}
.header-default .header-bottom.style-2 .wrapper-header {
  min-height: 65px;
}
.header-default .header-bottom.style-2 .wrapper-header .menu-item {
  padding: 20px 0px;
}
.header-default .header-bottom .box-right {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-end;
}
.header-default .header-bottom .box-right .icon {
  font-size: 20px;
}
.header-default .header-bottom .box-left {
  display: flex;
  align-items: center;
}
.header-default .sub-categories2 {
  position: relative;
}
.header-default .sub-categories2 .list-categories-inner {
  left: 101%;
  margin: 0;
  top: 0;
  min-width: 250px;
}
.header-default .sub-categories2 .list-categories-inner::before {
  height: 70px;
  position: absolute;
  width: 25px;
  top: 0;
  left: -12px;
  bottom: 0;
  content: "";
}
.header-default .sub-categories2 .list-categories-inner::after {
  content: none !important;
}
.header-default .sub-categories2:hover .list-categories-inner {
  opacity: 1;
  visibility: visible;
  transform: none;
  pointer-events: all;
}
.header-default .main-header .wrapper-header {
  min-height: auto;
  padding-top: 22px;
  padding-bottom: 22px;
}
.header-default .mobile-menu {
  display: inline-flex;
}
.header-default .mobile-menu .icon {
  font-size: 24px;
}
.header-default .box-support {
  display: flex;
  align-items: center;
  gap: 12px;
}
.header-default .box-support .icon {
  font-size: 32px;
}

.tf-list-categories {
  position: relative;
}
.tf-list-categories .categories-title {
  padding: 14px 16px;
  background-color: var(--main);
  color: var(--white);
  border-radius: 5px;
  display: inline-flex;
  align-items: center;
  gap: 12px;
}
.tf-list-categories .categories-title .text {
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 1px;
  font-weight: 600;
}
.tf-list-categories .categories-title .icon {
  font-size: 20px;
}
.tf-list-categories .list-categories-inner {
  left: 0;
  border-radius: 8px;
  position: absolute;
  background-color: var(--white);
  min-width: 300px;
  z-index: 3;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  text-align: left;
  box-shadow: var(--shadow1);
  top: 100%;
  margin-top: 10px;
  color: var(--main);
  pointer-events: none;
  padding: 8px 0px;
}
.tf-list-categories .list-categories-inner::before {
  height: 20px;
  position: absolute;
  top: -15px;
  left: 0;
  right: 0;
  content: "";
}
.tf-list-categories .categories-item {
  padding: 9px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}
.tf-list-categories .categories-item .icon {
  font-size: 20px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-list-categories .categories-item .inner-left {
  display: flex;
  align-items: center;
  flex-grow: 1;
}
.tf-list-categories .categories-item .inner-left .icon {
  font-size: 24px;
  margin-right: 12px;
  color: var(--secondary-2);
}
.tf-list-categories .categories-item:hover {
  color: var(--primary);
}
.tf-list-categories .categories-item:hover .icon {
  color: var(--primary);
}
.tf-list-categories:hover > .list-categories-inner {
  opacity: 1;
  visibility: visible;
  transform: none;
  pointer-events: all;
}
.tf-list-categories .box-cate-bottom {
  position: relative;
  padding-top: 9px;
  margin-top: 9px;
}
.tf-list-categories .box-cate-bottom::before {
  content: "";
  position: absolute;
  height: 1px;
  background-color: var(--line);
  top: 0;
  left: 20px;
  right: 20px;
}
.tf-list-categories.style-1 {
  border: 1px solid var(--line);
  border-radius: 8px;
}
.tf-list-categories.style-1 .categories-title {
  border-radius: 8px 8px 0 0;
  padding: 13px 20px;
  display: flex;
  background-color: rgb(45, 48, 84);
}
.tf-list-categories.style-1 .categories-title .text {
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
}
.tf-list-categories.style-1 .categories-item {
  padding: 14px 20px;
}
.tf-list-categories.style-1 .categories-item:hover .icon {
  color: var(--primary);
}
.tf-list-categories.style-1 .icon-left {
  font-size: 24px;
}
.tf-list-categories.style-1 .sub-categories2 {
  position: relative;
}
.tf-list-categories.style-1 .sub-categories2 .list-categories-inner {
  left: 100%;
  margin: 0;
}
.tf-list-categories.style-1 .sub-categories2:hover .list-categories-inner {
  opacity: 1;
  visibility: visible;
  top: 0;
  pointer-events: all;
  transform: translate(0);
}
.tf-list-categories.style-1 ul li:not(:last-child) {
  border-bottom: 1px solid var(--line);
}
.tf-list-categories.style-1 > .list-categories-inner {
  position: unset;
  opacity: 1;
  visibility: visible;
  transform: none;
  pointer-events: all;
  margin: 0;
  min-width: unset;
  box-shadow: none;
  padding: 0;
}
.tf-list-categories.style-1 > .list-categories-inner .icon {
  color: var(--main);
}
.tf-list-categories.style-2 .categories-title {
  gap: 10px;
  border-radius: 12px;
  color: var(--main);
  background-color: rgba(60, 52, 228, 0.1019607843);
}
.tf-list-categories.style-2 .categories-title .icon {
  font-size: 12px;
}
.tf-list-categories.style-2 .list-categories-inner {
  min-width: 240px;
  margin-top: 0;
}
.tf-list-categories.style-2 .list-categories-inner li:not(:last-child) {
  border-bottom: 1px solid var(--line);
}

#header .box-nav-ul .item-link {
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: relative;
}
#header .box-nav-ul .item-link .icon {
  font-size: 12px;
  font-weight: 600;
}
#header .box-nav-ul .item-link::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: calc(100% + 40px);
  height: 82px;
  display: none;
}
#header .box-nav-ul .menu-item.active .item-link, #header .box-nav-ul .menu-item:hover .item-link {
  color: var(--primary);
}
#header .box-nav-ul .menu-item.active .demo-item.active .demo-name {
  color: var(--primary);
}
#header .box-nav-ul .menu-item:hover .item-link::after {
  display: block;
}
#header .nav-icon .icon {
  font-size: 24px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
#header .nav-icon > li {
  display: inline-flex;
}
#header .nav-icon .nav-icon-item {
  position: relative;
  display: inline-flex;
}
#header .nav-icon .nav-icon-item svg path {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
#header .nav-icon .nav-icon-item:hover svg path {
  stroke: var(--primary);
}
#header .nav-icon .nav-icon-item:hover .icon {
  color: var(--primary);
}
#header .nav-icon .nav-icon-item .text {
  font-size: 16px;
  line-height: 25.6px;
  font-weight: 500;
  display: inline-block;
}
#header .nav-icon .count-box {
  position: absolute;
  line-height: 14px;
  height: 14px;
  min-width: 14px;
  text-align: center;
  padding: 0 3px;
  font-size: 10px;
  font-weight: 500;
  border-radius: 9999px;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden;
  z-index: 2;
  top: -2px;
  right: -2px;
  color: var(--white);
  background-color: var(--primary);
}
#header .tf-product-header .card-product .box-icon {
  width: 32px;
  height: 32px;
  min-width: 32px;
}
#header .tf-product-header .card-product .box-icon .icon {
  font-size: 20px;
}
#header .tf-product-header .card-product .btn-main-product {
  padding: 5px 10px;
}
#header .sub-menu .hover-sw-nav .nav-sw {
  width: 36px;
  height: 36px;
  top: 38%;
}
#header .sub-menu .list-color-item .tooltip {
  display: none;
}

.header-absolute {
  margin-bottom: -64px;
  background-color: transparent;
  z-index: 999;
}

.header-white .box-nav-ul .item-link {
  color: var(--white);
}
.header-white .nav-icon .icon {
  color: var(--white);
}
.header-white .nav-icon svg path {
  stroke: var(--white);
}
.header-white .mobile-menu .icon {
  color: var(--white);
}
.header-white.header-bg {
  background-color: var(--main);
}

.logo-header img {
  width: 144px;
}

.box-nav-ul .menu-item {
  padding: 28px 0px;
}
.box-nav-ul .menu-item:hover > .sub-menu {
  pointer-events: all;
  opacity: 1;
  visibility: visible;
  transform: translateX(0px) translateY(0px);
}
.box-nav-ul .menu-item-2 {
  position: relative;
  cursor: pointer;
}
.box-nav-ul .menu-item-2::after {
  position: absolute;
  content: "\e934";
  font-family: "icomoon";
  font-size: 11px;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.box-nav-ul .menu-item-2 .sub-menu {
  top: 0px;
  left: 110%;
  min-width: 200px;
}
.box-nav-ul .menu-item-2 .sub-menu::after {
  position: absolute;
  display: block;
  content: "";
  width: 60px;
  height: 45px;
  background-color: transparent;
  left: -18%;
  top: 3px;
}
.box-nav-ul .menu-item-2:hover > .sub-menu {
  pointer-events: all;
  opacity: 1;
  visibility: visible;
  transform: translateX(0px) translateY(0px);
}
.box-nav-ul .sub-menu {
  pointer-events: none;
  position: absolute;
  background-color: var(--white);
  min-width: 200px;
  z-index: 999;
  visibility: hidden;
  text-align: left;
  padding: 8px 20px;
  top: 100%;
  opacity: 0;
  visibility: hidden;
  border-radius: 8px;
  transform: translateX(0px) translateY(10px);
  box-shadow: var(--shadow1);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.box-nav-ul .wrapper-sub-shop {
  margin-left: -10px;
}
.box-nav-ul .wrapper-sub-shop .menu-heading {
  margin-bottom: 15px;
}
.box-nav-ul .mega-menu {
  padding-top: 32px;
  padding-bottom: 36px;
  border: none;
  max-height: calc(100vh - 74px);
  overflow: auto;
  left: 0;
  right: 0;
}
.box-nav-ul .mega-menu .card-product .card-product-wrapper {
  max-height: 290px;
}
.box-nav-ul .mega-menu .wrap-sw-over {
  padding-bottom: 40px;
  margin-bottom: -40px;
}
.box-nav-ul .menu-heading {
  font-size: 12px;
  line-height: 20px;
  font-weight: 700;
  color: var(--main);
  text-transform: uppercase;
  margin-bottom: 8px;
  letter-spacing: 1px;
  text-align: start;
}
.box-nav-ul .menu-list .menu-link-text {
  padding: 7px 0px;
  color: var(--secondary);
  text-transform: capitalize;
  position: relative;
}
.box-nav-ul .menu-list .menu-link-text::before {
  content: "";
  width: 0;
  height: 1px;
  top: 80%;
  position: absolute;
  left: auto;
  right: 0;
  z-index: 1;
  -webkit-transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  -o-transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  background: var(--main);
}
.box-nav-ul .menu-list .menu-link-text:hover {
  color: var(--main);
}
.box-nav-ul .menu-list .menu-link-text:hover::before {
  width: 100%;
  left: 0;
  right: auto;
}
.box-nav-ul .menu-list .menu-link-text.active {
  color: var(--main);
}
.box-nav-ul .menu-list .menu-link-text.active::before {
  width: 100%;
  left: 0;
  right: auto;
}
.box-nav-ul .menu-list .demo-label {
  top: -4px;
  left: calc(100% + 5px);
}
.box-nav-ul .submenu-default {
  left: -30px;
  width: max-content;
}
.box-nav-ul .view-all-demo .tf-btn {
  padding: 10px 28px;
}
.box-nav-ul .sec-cls-header {
  margin-top: 15px;
  padding-bottom: 78px;
}
.box-nav-ul .collection-position .content {
  gap: 28px;
}
.box-nav-ul .collection-position .content .title-top {
  display: grid;
  gap: 8px;
}

.mega-menu .row-demo {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  overflow-y: auto;
  padding-right: 10px;
  margin-right: -16px;
}
.mega-menu .row-demo::-webkit-scrollbar {
  width: 6px;
}
.mega-menu .row-demo::-webkit-scrollbar-thumb {
  background: var(--line);
}
.mega-menu .demo-item {
  border-radius: 8px;
  background-color: var(--surface);
  padding: 8px 8px 0px;
  border: solid 1px var(--line);
  transition: border 0.4s;
  margin-bottom: 1px;
  display: inline-flex;
}
.mega-menu .demo-item .demo-name {
  font-size: 14px;
  line-height: 42px;
  display: block;
  text-wrap: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: center;
  font-weight: 600;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.mega-menu .demo-item .demo-name:hover {
  color: var(--primary);
}
.mega-menu .demo-item:hover {
  border-color: var(--main);
}
.mega-menu .view-all-demo {
  margin-top: 28px;
}
.mega-menu .collection-item .collection-title {
  background-color: #f2f2f2;
  border-radius: 3px;
}
.mega-menu .collection-item .collection-content {
  bottom: 40px;
}
.mega-menu .collection-item .tf-btn .icon {
  margin: 0px;
}

.mega-page {
  max-width: 900px;
  margin: auto;
}

.demo-label {
  position: absolute;
  top: 9px;
  right: 7px;
  gap: 5px;
  display: flex;
}
.demo-label span {
  font-size: 10px;
  line-height: 19px;
  padding: 0 8px;
  background-color: rgb(131, 183, 53);
  color: var(--white);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.demo-label .demo-new {
  background-color: rgb(72, 212, 187);
}
.demo-label .demo-hot {
  background-color: rgb(252, 87, 50);
}

.canvas-mb {
  width: 100% !important;
  max-width: min(90%, 320px);
  border-right: 0 !important;
}
.canvas-mb .mb-canvas-content {
  padding-top: 60px;
  min-width: 100%;
  max-width: min(90%, 320px);
  grid-auto-rows: minmax(0, 1fr) auto;
  isolation: isolate;
  height: 100%;
  width: 100%;
  display: grid;
  align-content: start;
}
.canvas-mb .mb-body {
  padding-right: 20px;
  padding-left: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--line);
  overscroll-behavior-y: contain;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.canvas-mb .icon-close-popup {
  position: absolute;
  font-size: 16px;
  z-index: 3;
  top: 20px;
  left: 15px;
  background-color: transparent;
  border: none;
  height: 30px;
  width: 30px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--main);
}
.canvas-mb .mb-bottom {
  padding-left: 20px;
  padding-right: 20px;
}
.canvas-mb .mb-bottom .site-nav-icon {
  margin-bottom: 18px;
}
.canvas-mb .mb-bottom .bottom-bar-language {
  min-height: 40px;
  max-width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
}
.canvas-mb .mb-bottom .bottom-bar-language .image-select.type-currencies > .dropdown-menu {
  margin-left: 0 !important;
}
.canvas-mb .mb-bottom .bottom-bar-language .image-select > .dropdown-menu::before {
  display: none;
}
.canvas-mb .mb-bottom .bottom-bar-language .tf-currencies,
.canvas-mb .mb-bottom .bottom-bar-language .tf-languages {
  display: flex;
  align-items: center;
  justify-content: center;
}
.canvas-mb .mb-bottom .bottom-bar-language .tf-currencies {
  border-right: 1px solid var(--line);
}
.canvas-mb .site-nav-icon {
  padding: 0 9px;
  line-height: 40px;
  border: solid 1px var(--line);
  gap: 4px;
  background-color: var(--line);
  color: var(--main);
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
}
.canvas-mb .site-nav-icon .icon {
  font-size: 14px;
}
.canvas-mb .site-nav-icon svg path {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.canvas-mb .site-nav-icon:hover {
  color: var(--white);
  background-color: var(--main);
  border-color: var(--main);
}
.canvas-mb .site-nav-icon:hover svg path {
  stroke: var(--white);
}
.canvas-mb .mb-other-content .group-icon {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}
.canvas-mb .mb-other-content .text-need {
  font-weight: 500;
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}
.canvas-mb .mb-other-content .mb-info li {
  font-size: 14px;
  line-height: 22px;
  display: flex;
  align-items: center;
}
.canvas-mb .mb-other-content .mb-info li .icon {
  font-size: 20px;
  margin-right: 12px;
}
.canvas-mb .mb-other-content .mb-info li:not(:last-child) {
  margin-bottom: 8px;
}
.canvas-mb .mb-other-content .mb-contact {
  display: grid;
  gap: 8px;
  margin-bottom: 12px;
}
.canvas-mb .mb-other-content .mb-contact .tf-btn-default i {
  font-size: 16px;
}
.canvas-mb .form-search {
  margin-bottom: 8px;
}
.canvas-mb .form-search input {
  padding: 12px;
  padding-left: 44px;
  font-size: 14px;
  line-height: 22px;
  color: var(--secondary-2);
  border-width: 1px;
}
.canvas-mb .form-search button {
  right: auto;
  left: 12px;
}

.nav-ul-mb .nav-mb-item {
  padding: 2px 0px;
}
.nav-ul-mb .nav-mb-item:not(:last-child) {
  border-bottom: 1px solid var(--line);
}
.nav-ul-mb .nav-mb-item .mb-menu-link {
  min-height: 48px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: var(--main);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.nav-ul-mb .nav-mb-item .mb-menu-link:not(.collapsed) .btn-open-sub::before {
  transform: rotate(90deg);
}
.nav-ul-mb .nav-mb-item.active .mb-menu-link {
  font-weight: 600;
}
.nav-ul-mb .nav-mb-item.active .sub-nav-link.active {
  font-weight: 600;
}
.nav-ul-mb .btn-open-sub {
  position: relative;
  width: 20px;
  height: 30px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.nav-ul-mb .btn-open-sub:after, .nav-ul-mb .btn-open-sub::before {
  content: "";
  position: absolute;
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--main);
  transition: 0.4s ease 0.1s;
  margin: auto;
}
.nav-ul-mb .btn-open-sub::before {
  width: 2px;
  height: 12px;
}
.nav-ul-mb .btn-open-sub::after {
  width: 12px;
  height: 2px;
}
.nav-ul-mb .sub-nav-menu {
  padding-left: 10px;
  margin-bottom: 15px;
}
.nav-ul-mb .sub-menu-level-2 {
  margin-bottom: 5px;
}
.nav-ul-mb .sub-nav-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 36px;
  line-height: 36px;
  font-size: 14px;
  color: var(--secondary);
}
.nav-ul-mb .sub-nav-link:not(.collapsed) .btn-open-sub::before {
  transform: rotate(90deg);
}
.nav-ul-mb .sub-nav-link .btn-open-sub::after, .nav-ul-mb .sub-nav-link .btn-open-sub::before {
  background-color: var(--secondary);
}
.nav-ul-mb .sub-nav-link.line-clamp {
  position: relative;
  display: inline-flex;
}
.nav-ul-mb .sub-nav-link.line-clamp .demo-label {
  top: -5px;
  right: -38px;
}

.header-list-categories {
  display: flex;
  align-items: center;
  gap: 26px;
}
.header-list-categories .categories-item {
  position: relative;
  display: flex;
  align-items: center;
}
.header-list-categories .categories-item a {
  font-weight: 600;
  font-size: 16px;
  line-height: 26px;
  transition: all 0.2s;
  padding: 4px 0px;
}
.header-list-categories .categories-item a::after {
  content: "";
  position: absolute;
  width: 0;
  bottom: 0;
  height: 2px;
  left: 50%;
  transform: translateX(-50%);
  -webkit-transition: all 0.2s;
  -ms-transition: all 0.2s;
  transition: all 0.2s;
  background-color: var(--main);
}
.header-list-categories .categories-item.active a::after, .header-list-categories .categories-item:hover a::after {
  width: 100%;
}

.header-style-2 .logo-header {
  margin-left: 30px;
}
.header-style-2 .logo-header img {
  width: 130px;
}

.header-style-4 .main-header .wrapper-header {
  padding-bottom: 26px;
}
.header-style-4 .wrapper-header-right .tf-currencies {
  padding-right: 17px;
}
.header-style-4 .wrapper-header-right .image-select.style-default > .dropdown-toggle {
  padding-right: 27px;
}

.header-style-5 .main-header .wrapper-header {
  min-height: 68px;
  padding-top: 12px;
  padding-bottom: 12px;
}
.header-style-5 .logo-header img {
  width: 130px;
}
.header-style-5 .wrapper-header-left .form-search-select {
  margin-left: 60px;
}
.header-style-5 .header-bottom .wrapper-header {
  min-height: 50px;
}
.header-style-5 .header-bottom .box-nav-ul {
  margin-left: 40px;
}
.header-style-5 .header-bottom .box-nav-ul .menu-item {
  padding: 12px 0px;
}
.header-style-5 .header-bottom .btn-select {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--white);
}
.header-style-5 .header-bottom .tf-dropdown-sort {
  padding: 5px 0px;
  min-width: 146px;
  border: none;
}
.header-style-5 .header-bottom .tf-dropdown-sort .dropdown-menu {
  margin-top: 6px !important;
}
.header-style-5 .tf-list-categories .categories-title {
  background-color: transparent;
  padding: 4px 0px;
  gap: 8px;
  padding-right: 40px;
  border-right: 1px solid rgba(233, 233, 233, 0.1);
}
.header-style-5 .tf-list-categories .categories-title .text {
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 0;
}
.header-style-5 .tf-list-categories .categories-title .icon-left {
  font-size: 24px;
}
.header-style-5 .tf-list-categories .categories-title .icon {
  font-size: 17px;
}
.header-style-5 .tf-list-categories .list-categories-inner {
  margin-top: 8px;
}
.header-style-5 .tf-list-categories .list-categories-inner::after {
  content: "";
  position: absolute;
  bottom: calc(100% - 16px);
  left: 26px;
  border-top: 20px solid var(--white);
  border-right: 20px solid transparent;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.header-style-6 {
  margin-bottom: -100px !important;
}
.header-style-6.mb-0 {
  margin-bottom: 0px !important;
}
.header-style-6 .box-nav-ul .menu-item {
  padding: 27px 0px;
}
.header-style-6 .wrapper-header {
  min-height: unset !important;
  margin-top: 20px;
  border-radius: 99px;
  background-color: var(--white);
  padding: 0px 20px 0 20px;
}
.header-style-6 .header-left {
  display: flex;
  align-items: center;
  gap: 60px;
}
.header-style-6 .header-right {
  display: flex;
  justify-content: end;
  align-items: center;
  gap: 48px;
}
.header-style-6 .header-right .form-search {
  width: 240px;
}
.header-style-6 .header-right .form-search::after {
  width: 1px;
  height: 24px;
  position: absolute;
  content: "";
  background-color: #e9e9e9;
  right: -24px;
  top: 50%;
  transform: translateY(-50%);
}
.header-style-6 .header-right .form-search button {
  right: 0;
}
.header-style-6.header-bg {
  background-color: transparent;
  box-shadow: none;
}
.header-style-6.header-bg .wrapper-header {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tf-topbar.type-1 {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  z-index: 10;
}

.header-style-7 .logo-header img {
  width: 130px;
}
.header-style-7 .form-search {
  width: 240px;
}

.header-bottom .box-nav-ul .menu-item {
  padding: 16px 0px;
}

.wrapper-header-left {
  display: flex;
  align-items: center;
}
.wrapper-header-left .box-navigation {
  padding-left: 60px;
}

header {
  position: sticky;
  position: -webkit-sticky;
  left: 0;
  right: 0;
  -webkit-transition: 0.2s ease-out;
  -o-transition: 0.2s ease-out;
  transition: 0.2s ease-out;
  z-index: 888;
  background-color: var(--white);
}
header.header-bg {
  background-color: var(--white);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-scroll-white .dark-logo {
  display: none;
}
.header-scroll-white.header-bg {
  background-color: var(--white);
}
.header-scroll-white.header-bg .dark-logo {
  display: block;
}
.header-scroll-white.header-bg .white-logo {
  display: none;
}
.header-scroll-white.header-bg .box-nav-ul .item-link,
.header-scroll-white.header-bg .nav-icon .nav-icon-item {
  color: var(--main);
}
.header-scroll-white.header-bg .box-nav-ul .item-link::before {
  background-color: var(--main) !important;
}
.header-scroll-white.header-bg .btn-mobile svg path {
  fill: var(--main);
}

.header-dark {
  background-color: var(--main);
}
.header-dark .box-nav-ul .item-link {
  color: var(--white);
}
.header-dark .header-list-categories .categories-item a {
  color: var(--white);
  font-size: 12px;
  line-height: 22px;
}
.header-dark .header-list-categories .categories-item a::after {
  background-color: var(--white);
  height: 1px;
}

.header-fullwidth,
.header-fullwidth-2 {
  padding-left: 15px;
  padding-right: 15px;
}

.nav-account {
  position: relative;
}
.nav-account .dropdown-account {
  pointer-events: none;
  position: absolute;
  background-color: var(--white);
  min-width: 200px;
  z-index: 999;
  visibility: hidden;
  text-align: left;
  top: calc(100% + 28px);
  opacity: 0;
  visibility: hidden;
  border-radius: 8px;
  transform: translateX(0px) translateY(15px);
  box-shadow: var(--shadow1);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  left: -100px;
  padding: 8px 28px;
}
.nav-account .dropdown-account .list-menu-item li:not(:last-child) {
  margin-bottom: 8px;
}
.nav-account .dropdown-account .list-menu-item li:not(:last-child) a {
  border-bottom: 1px solid var(--line);
}
.nav-account .dropdown-account .list-menu-item a {
  padding: 12px 0px;
  display: block;
}
.nav-account:hover .dropdown-account {
  pointer-events: all;
  opacity: 1;
  visibility: visible;
  transform: translateX(0px) translateY(0px);
}
.nav-account:hover .nav-icon-item::after {
  display: block;
}
.nav-account .dropdown-login {
  left: -200px;
  min-width: 290px;
  padding: 24px;
}
.nav-account .dropdown-login .tf-btn {
  border-radius: 4px;
  width: 100%;
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
}
.nav-account .dropdown-login .sub-top {
  display: grid;
  gap: 12px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--line);
}
.nav-account .dropdown-login .sub-bot {
  padding: 16px 0px;
}
.nav-account .nav-icon-item {
  position: relative;
}
.nav-account .nav-icon-item::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 82px;
  display: none;
}

.modalDemo .demo-item.active .demo-name {
  color: var(--primary);
}

/*------------ footer ---------------- */
footer .footer-wrap {
  border-top: 1px solid var(--line);
}
footer .footer-body {
  padding: 80px 0;
}
footer .footer-bottom-wrap {
  padding-top: 14px;
  border-top: 1px solid var(--line);
  padding-bottom: 17px;
  display: flex;
  gap: 15px 30px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
footer .footer-bottom-wrap .left {
  display: flex;
  gap: 15px 40px;
  align-items: center;
  flex-wrap: wrap;
}
footer .footer-infor {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
footer .footer-infor .footer-logo > a {
  display: flex;
}
footer .footer-address {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
footer .tf-btn-default {
  letter-spacing: 0.1em;
  padding-bottom: 2px;
}
footer .tf-btn-default i {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: var(--main);
}
footer .tf-btn-default:hover i {
  color: var(--primary) !important;
  transform: rotate(45deg);
}
footer .tf-btn-default.style-white i {
  color: var(--white);
}
footer .footer-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
footer .footer-info li {
  display: flex;
  align-items: center;
  gap: 12px;
}
footer .footer-info li i {
  color: var(--main);
  font-size: 20px;
}
footer .footer-menu {
  display: flex;
  gap: 15px;
}
footer .footer-menu .footer-menu_item {
  color: var(--secondary);
}
footer .footer-menu .footer-menu_item:hover {
  color: var(--primary);
}
footer .footer-menu .footer-menu-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
footer .footer-newsletter {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
footer .tf-payment ul img {
  width: 38px;
  height: 24px;
}
footer p {
  color: var(--secondary);
}
footer .footer-heading {
  margin-bottom: 12px;
}
footer.bg-main .footer-wrap {
  border: 0;
}
footer.bg-main .footer-heading {
  color: var(--white);
}
footer.bg-main .footer-info li i {
  color: var(--white);
}
footer.bg-main .tf-cart-checkbox a {
  color: var(--white);
}
footer.bg-main .tf-cart-checkbox label {
  color: var(--secondary-2);
}
footer.bg-main p {
  color: var(--secondary-2);
}
footer.bg-main .footer-newsletter p a {
  color: var(--white);
}
footer.bg-main .footer-menu .footer-menu_item {
  color: var(--secondary-2);
}
footer.bg-main .footer-menu .footer-menu_item:hover {
  color: var(--primary);
}
footer.bg-main .footer-bottom-wrap {
  border-color: rgba(255, 255, 255, 0.1);
}
footer.bg-main .footer-heading-mobile::after,
footer.bg-main .footer-heading-mobile::before {
  background-color: var(--white);
}
footer.has-pb {
  padding-bottom: 98px;
}

.tf-payment {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.tf-payment ul {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.tf-payment ul img {
  width: 36px;
}

.tf-cur {
  display: flex;
  gap: 20px;
}

.tf-toolbar-bottom {
  display: none;
  padding: 15px 0px;
  overflow-x: auto;
  overflow-y: hidden;
  position: fixed;
  z-index: 60;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: var(--white);
  box-shadow: var(--shadow2);
}
.tf-toolbar-bottom .toolbar-item {
  flex: 1 0 20%;
  position: relative;
}
.tf-toolbar-bottom .toolbar-item a {
  width: 100%;
  padding-right: 10px;
  padding-left: 10px;
  height: 40px;
  gap: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.tf-toolbar-bottom .toolbar-item a .toolbar-icon {
  position: relative;
}
.tf-toolbar-bottom .toolbar-item a .toolbar-icon i {
  font-size: 20px;
  color: var(--main);
}
.tf-toolbar-bottom .toolbar-item a .toolbar-icon .toolbar-count {
  position: absolute;
  top: -4px;
  right: -8px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  border-radius: 50%;
  font-size: 10px;
  font-weight: 500;
  line-height: 18px;
  color: var(--white);
}
.tf-toolbar-bottom .toolbar-item a .toolbar-label {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  color: var(--secondary);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-toolbar-bottom .toolbar-item a:hover .toolbar-label {
  color: var(--main);
  font-weight: 600;
}

/*------------ tabs ---------------- */
.widget-tabs .widget-menu-tab .item-title {
  cursor: pointer;
  position: relative;
}
.widget-tabs .widget-menu-tab .item-title::after {
  position: absolute;
  content: "";
  background-color: var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.widget-tabs .widget-content-tab {
  position: relative;
  overflow: hidden;
}
.widget-tabs .widget-content-tab .widget-content-inner {
  display: block;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  -webkit-transform: translateY(30px);
  -ms-transform: translateY(30px);
  transform: translateY(30px);
  transition-timing-function: ease-in;
  transition-duration: 0.2s;
}
.widget-tabs .widget-content-tab .widget-content-inner.active {
  pointer-events: auto;
  opacity: 1;
  visibility: visible;
  position: relative;
  z-index: 2;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
  transition-timing-function: ease-out;
  transition-duration: 0.3s;
  transition-delay: 0.3s;
}
.widget-tabs.style-1 .widget-menu-tab {
  display: flex;
  align-items: center;
  gap: 40px;
  justify-content: center;
  padding-bottom: 28px;
  overflow-x: auto;
}
.widget-tabs.style-1 .widget-menu-tab .item-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 26px;
  min-width: max-content;
}
.widget-tabs.style-1 .widget-menu-tab .item-title::after {
  position: absolute;
  content: "";
  background-color: var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
}
.widget-tabs.style-1 .widget-menu-tab .item-title.active::after {
  width: 100%;
}
.widget-tabs.style-1 .widget-content-inner {
  padding: 39px;
  border: 1px solid var(--line);
  border-radius: 8px;
}
.widget-tabs.style-menu-tabs {
  display: flex;
  gap: 60px;
}
.widget-tabs.style-menu-tabs .widget-menu-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 196px;
  flex-shrink: 0;
  height: max-content;
  overflow-x: auto;
  overflow-y: hidden;
}
.widget-tabs.style-menu-tabs .widget-menu-tab .item-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.widget-tabs.style-menu-tabs .widget-menu-tab .item-title::after {
  position: absolute;
  content: "";
  background-color: var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  top: 0;
  left: 0;
  width: 2px;
  height: 0;
}
.widget-tabs.style-menu-tabs .widget-menu-tab .item-title.active {
  padding-left: 16px;
}
.widget-tabs.style-menu-tabs .widget-menu-tab .item-title.active::after {
  height: 100%;
}
.widget-tabs.style-menu-tabs .widget-content-tab {
  flex-grow: 1;
}
.widget-tabs.style-menu-tabs .widget-content-inner {
  border-radius: 8px;
  padding: 40px;
  border: 1px solid var(--line);
}
.widget-tabs.style-2 .widget-menu-tab {
  padding: 3px;
  border-radius: 12px;
  border: 1px solid var(--line);
  display: flex;
  align-items: center;
  gap: 8px;
}
.widget-tabs.style-2 .widget-menu-tab .item-title {
  padding: 7px 20px;
  border-radius: 12px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.widget-tabs.style-2 .widget-menu-tab .item-title.active {
  background-color: var(--line);
}
.widget-tabs.style-3 .widget-menu-tab {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-bottom: 16px;
  overflow-x: auto;
  border-bottom: 1px solid var(--line);
}
.widget-tabs.style-3 .widget-menu-tab .item-title {
  padding: 7px 0;
  min-width: max-content;
}
.widget-tabs.style-3 .widget-menu-tab .item-title::after {
  position: absolute;
  content: "";
  background-color: var(--primary);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  bottom: 0px;
  left: 0;
  width: 0;
  height: 2px;
}
.widget-tabs.style-3 .widget-menu-tab .item-title.active::after {
  width: 100%;
}

.tab-description {
  display: flex;
  gap: 32px;
}
.tab-description .right,
.tab-description .left {
  width: 100%;
}
.tab-description ul.list-text.type-disc li {
  padding-left: 24px;
}
.tab-description ul.list-text.type-disc li:before {
  top: 11px;
  left: 11px;
  width: 3px;
  height: 3px;
  border-radius: 0;
}

.tab-reviews .tab-reviews-heading {
  margin-bottom: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 50px;
  flex-wrap: wrap;
}
.tab-reviews .tab-reviews-heading .top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
  width: 100%;
  max-width: 597px;
  gap: 30px;
}
.tab-reviews .tab-reviews-heading .top .list-start {
  display: flex;
  gap: 2px;
  justify-content: center;
  margin-bottom: 7px;
  margin-top: 1px;
  font-size: 17px;
}
.tab-reviews .rating-score {
  width: 100%;
  max-width: 365px;
}
.tab-reviews .rating-score .item {
  width: 100%;
  display: flex;
  align-items: center;
}
.tab-reviews .rating-score .item:not(:last-child) {
  margin-bottom: 4px;
}
.tab-reviews .rating-score .number-1 {
  width: 12px;
  text-align: end;
}
.tab-reviews .rating-score .icon {
  font-size: 15px;
  margin-left: 4px;
}
.tab-reviews .rating-score .number-2 {
  width: 17px;
}
.tab-reviews .rating-score .line-bg {
  margin: 0 8px;
  width: 100%;
  height: 8px;
  background-color: var(--line);
}
.tab-reviews .rating-score .line-bg div {
  height: 100%;
  background-color: var(--main);
}

.tab-shipping {
  display: flex;
  gap: 30px;
}
.tab-shipping p {
  color: var(--secondary);
}

.tab-product {
  gap: 20px;
  margin-bottom: 20px;
  overflow-x: auto;
  display: flex;
}
.tab-product .nav-tab-item a {
  display: flex;
  width: 100%;
  font-size: 24px;
  line-height: 34px;
  font-weight: 500;
  white-space: nowrap;
  padding-bottom: 4px;
  border-bottom: 2px solid transparent;
  text-transform: capitalize;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: var(--secondary);
}
.tab-product .nav-tab-item a:hover, .tab-product .nav-tab-item a.active {
  color: var(--main);
  border-bottom-color: var(--main);
}
.tab-product::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

.tab-product-v2 {
  gap: 26px;
  margin-top: 24px;
  overflow-x: auto;
  display: flex;
}
.tab-product-v2 .nav-tab-item a {
  display: flex;
  width: 100%;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  white-space: nowrap;
  padding-bottom: 4px;
  padding-top: 4px;
  border-bottom: 2px solid transparent;
  text-transform: capitalize;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: var(--secondary);
}
.tab-product-v2 .nav-tab-item a:hover, .tab-product-v2 .nav-tab-item a.active {
  color: var(--main);
  border-bottom-color: var(--main);
}
.tab-product-v2::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

.tab-product-v3 {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  max-width: 100%;
}
.tab-product-v3 a {
  width: max-content;
  padding: 4px 15px;
  border-radius: 99px;
  border: 1px solid var(--line);
}
.tab-product-v3 a:hover, .tab-product-v3 a.active {
  color: var(--primary);
  background-color: rgba(228, 49, 49, 0.1019607843);
  border-color: var(--Critical);
}

.tab-product-v4 {
  display: flex;
  gap: 5px 29px;
  overflow-x: auto;
  max-width: 100%;
}
.tab-product-v4 a {
  position: relative;
  width: max-content;
  padding: 4px 0px;
  border-radius: 99px;
  color: var(--secondary);
}
.tab-product-v4 a:hover, .tab-product-v4 a.active {
  color: var(--main);
}
.tab-product-v4 .nav-tab-item:not(:last-child) a::after {
  position: absolute;
  content: "";
  right: -23px;
  top: 50%;
  width: 20px;
  height: 1px;
  transform: translateY(-50%) rotate(280deg);
  background-color: var(--line);
}

.flat-animate-tab {
  overflow: hidden;
}
.flat-animate-tab .tab-content {
  position: relative;
}
.flat-animate-tab .tab-pane {
  display: block;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  -webkit-transform: translateY(30px);
  -ms-transform: translateY(30px);
  transform: translateY(30px);
  transition-timing-function: ease-in;
  transition-duration: 0.2s;
}
.flat-animate-tab .tab-pane.active {
  pointer-events: auto;
  opacity: 1;
  visibility: visible;
  position: relative;
  z-index: 2;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
  transition-timing-function: ease-out;
  transition-duration: 0.3s;
  transition-delay: 0.3s;
}
.flat-animate-tab .sec-btn {
  margin-top: 20px;
}

.tab-policies ul.list-text li {
  padding-left: 24px;
}
.tab-policies ul.list-text.type-number {
  margin-bottom: 6px;
  gap: 0;
}

.tab-size {
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.tab-size .size-button-wrap {
  display: flex;
  gap: 32px;
}
.tab-size .size-button-wrap .size-button-item {
  width: 100%;
  padding: 24px 0;
  border-radius: 12px;
  border: 1px solid var(--line);
  background-color: var(--line);
  text-align: center;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.tab-size .size-button-wrap .size-button-item.select-option {
  border-color: var(--main);
}
.tab-size .size-button-wrap .size-button-item h5 {
  text-transform: capitalize;
}
.tab-size .suggests-title {
  margin-bottom: 16px;
}
.tab-size .suggests-list {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}
.tab-size .suggests-list .suggests-item {
  background-color: var(--line);
  border-radius: 999px;
  padding: 7px 20px;
}

.tab-sizeguide-table {
  border: 1px solid var(--line);
  border-radius: 5px;
  width: 100%;
}
.tab-sizeguide-table th {
  border: 1px solid var(--line);
  padding: 10px;
  font-weight: 600;
  line-height: 20px;
}
.tab-sizeguide-table td {
  border: 1px solid var(--line);
  border-width: 0 1px 1px 0;
  padding: 10px;
  line-height: 20px;
}

.tab-banner .nav-tab-item .nav-tab-link {
  padding-top: 15px;
  padding-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--line);
  gap: 8px;
  position: relative;
}
.tab-banner .nav-tab-item .nav-tab-link::after {
  position: absolute;
  content: "";
  width: 0;
  height: 1px;
  left: auto;
  right: 0;
  bottom: 0;
  background-color: var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tab-banner .nav-tab-item .nav-tab-link .arr-link {
  display: flex;
  align-items: center;
  gap: 8px;
}
.tab-banner .nav-tab-item .nav-tab-link .text-more {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tab-banner .nav-tab-item .nav-tab-link .icon {
  font-size: 16px;
}
.tab-banner .nav-tab-item .nav-tab-link:hover::after, .tab-banner .nav-tab-item .nav-tab-link.active::after {
  width: 100%;
  left: 0;
  right: auto;
}
.tab-banner .nav-tab-item .nav-tab-link:hover .text-more, .tab-banner .nav-tab-item .nav-tab-link.active .text-more {
  opacity: 1;
  visibility: visible;
}
.tab-banner .nav-tab-item:first-child .nav-tab-link {
  padding-top: 0;
}

/*------------ slider banner ---------------- */
.wrap-slider {
  position: relative;
}
.wrap-slider img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.wrap-slider .content-slider {
  display: grid;
  gap: 20px;
}
.wrap-slider .box-title-slider {
  display: grid;
  gap: 10px;
}
.wrap-slider .box-title-slider .subtitle {
  margin-bottom: 12px;
}
.wrap-slider .btn-square {
  max-width: 200px;
  width: 100%;
}

.swiper-slide .box-content {
  opacity: 0;
}
.swiper-slide.swiper-slide-active .box-content {
  opacity: 1;
}

.tf-slideshow {
  overflow: hidden;
  position: relative;
}
.tf-slideshow .wrap-pagination {
  position: absolute;
  z-index: 10;
  bottom: 15px;
  left: 0;
  right: 0;
}
.tf-slideshow .wrap-pagination .sw-dots {
  margin-top: 0;
}
.tf-slideshow .wrap-pagination.stype-space-2 {
  bottom: 20px;
}
.tf-slideshow .card-box {
  border-radius: 20px;
  padding: 20px;
  background-color: var(--white);
  overflow: hidden;
}
.tf-slideshow .box-content {
  position: absolute;
}
.tf-slideshow .sw-dots {
  margin: 0;
}
.tf-slideshow .sw-dots.type-circle .swiper-pagination-bullet {
  width: 20px;
  height: 20px;
}
.tf-slideshow .nav-sw {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.tf-slideshow .nav-sw.nav-sw-left {
  left: 30px;
}
.tf-slideshow .nav-sw.nav-sw-right {
  right: 30px;
}
.tf-slideshow:not(.slider-nav-sw) .nav-sw {
  visibility: hidden;
}
.tf-slideshow:not(.slider-nav-sw) .nav-sw.nav-sw-left {
  margin-left: 20px;
}
.tf-slideshow:not(.slider-nav-sw) .nav-sw.nav-sw-right {
  margin-right: 20px;
}
.tf-slideshow:not(.slider-nav-sw):hover .nav-sw {
  visibility: visible;
  margin: 0;
}
.tf-slideshow .slider-group {
  display: flex;
}
.tf-slideshow .slider-group img {
  width: 50%;
}
.tf-slideshow .btn-scroll-next {
  width: 44px;
  height: 44px;
  background-color: var(--white);
  border-radius: 50%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tf-slideshow .btn-scroll-next .icon {
  font-size: 20px;
}
.tf-slideshow .btn-scroll-next:hover {
  background-color: var(--main);
  color: var(--white);
}

.slider-default .box-content {
  left: 15px;
  right: 15px;
  bottom: 50px;
}
.slider-default.default-2 .box-content {
  left: 0;
  right: 0;
}

.slider-style2 .box-content {
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
}

.slider-center .box-content {
  text-align: center;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
}

.slider-effect-fade .swiper-slide .fade-item {
  transform: translateY(100px);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.slider-effect-fade .swiper-slide .fade-item.fade-box {
  transition-delay: 0.4s;
}
.slider-effect-fade .swiper-slide .fade-item.fade-item-1 {
  transition-delay: 0.5s;
}
.slider-effect-fade .swiper-slide .fade-item.fade-item-2 {
  transition-delay: 0.6s;
}
.slider-effect-fade .swiper-slide .fade-item.fade-item-3 {
  transition-delay: 0.7s;
}
.slider-effect-fade .swiper-slide .fade-item.fade-item-4 {
  transition-delay: 0.8s;
}
.slider-effect-fade .swiper-slide.swiper-slide-active .fade-item {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.slider-nav-sw .wrap-pagination {
  display: none;
}

.slider-padding {
  padding-left: 15px;
  padding-right: 15px;
}

.slider-radius-1 {
  border-radius: 8px;
  overflow: hidden;
}

.slider-radius-2 {
  border-radius: 12px;
  overflow: hidden;
}

.slider-radius-3 {
  border-radius: 20px;
  overflow: hidden;
}

.slider-collection .collection-position-2 {
  border-radius: 0;
}
.slider-collection .collection-position-2 .content {
  text-align: center;
  bottom: 20px;
}
.slider-collection .collection-position-2 .cls-btn {
  display: inline-flex;
}
.slider-collection .collection-position-2 .cls-btn .icon {
  font-size: 20px;
}

.slider-effect {
  position: relative;
}
.slider-effect.wrap-slider {
  height: auto !important;
}
.slider-effect .content-left {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
}
.slider-effect .content-left .box-content {
  position: unset;
  transform: unset;
}
.slider-effect .content-left img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.slider-effect .img-slider {
  width: 50%;
  margin-left: auto;
}

.slider-pet-store .wrap-slider {
  border-radius: 8px;
  overflow: hidden;
}
.slider-pet-store .wrap-slider .content-slider {
  padding: 0 60px;
}

.slider-video .wrap-slider {
  display: flex;
}

.slider-parallax .wrap-slider {
  background-attachment: scroll;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

/*------------ button ---------------- */
.tf-btn {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  background-color: var(--main);
  color: var(--white);
  padding: 15px 32px;
  border-radius: 99px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  text-transform: capitalize;
  border: 1px solid var(--main);
  position: relative;
  overflow: hidden;
}
.tf-btn span {
  color: inherit;
}
.tf-btn .icon {
  font-size: 22px;
}
.tf-btn .text,
.tf-btn .icon {
  position: relative;
  z-index: 2;
}
.tf-btn.btn-lg {
  padding: 16px 32px;
}
.tf-btn.btn-md {
  padding: 10px 24px;
}
.tf-btn:not(.btn-reset):after {
  content: "";
  position: absolute;
  bottom: -50%;
  width: 102%;
  height: 100%;
  background-color: var(--white);
  transform-origin: bottom center;
  transition: transform 600ms cubic-bezier(0.48, 0, 0.12, 1);
  transform: skewY(9.3deg) scaleY(0);
  z-index: 1;
}
.tf-btn:not(.btn-reset):hover {
  color: var(--main);
}
.tf-btn:not(.btn-reset):hover::after {
  transform-origin: bottom center;
  transform: skewY(9.3deg) scaleY(2);
}
.tf-btn.btn-reset:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}
.tf-btn.btn-white {
  background-color: var(--white);
  border: none;
  color: var(--main);
}
.tf-btn.btn-white:hover {
  color: var(--white);
}
.tf-btn.btn-white::after {
  background-color: var(--primary);
}
.tf-btn.btn-white.has-border {
  border: 1px solid var(--main);
}
.tf-btn.btn-white.has-border:hover {
  border-color: var(--primary);
}
.tf-btn.btn-square {
  border-radius: 8px;
}
.tf-btn.radius-4 {
  border-radius: 4px;
}

.btn-line {
  font-size: 16px;
  line-height: 26px;
  padding-bottom: 4px;
  font-weight: 600;
  color: var(--main);
  position: relative;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(to right, var(--primary) 50%, var(--main) 50%);
  background-size: 200% 100%;
  background-position: right;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: background-position 0.3s linear;
}
.btn-line::after {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  right: 0;
  height: 2px;
  background-color: var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.btn-line::before {
  position: absolute;
  content: "";
  left: 0;
  width: 0;
  bottom: 0;
  height: 2px;
  background-color: var(--primary);
  transition: width 0.3s linear;
  z-index: 1;
}
.btn-line.style-white {
  color: var(--white);
  background: linear-gradient(to right, var(--primary) 50%, var(--white) 50%);
  background-size: 200% 100%;
  background-position: right;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: background-position 0.3s linear;
}
.btn-line.style-white::after {
  background-color: var(--white);
}
.btn-line:hover {
  background-position: left;
}
.btn-line:hover::before {
  width: 100%;
}
.btn-line.has-icon {
  gap: 7px;
  padding-top: 4px;
}
.btn-line.has-icon i {
  font-size: 25px;
}

.tf-btn-default {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  line-height: 20px;
}
.tf-btn-default i {
  font-size: 20px;
}
.tf-btn-default:hover {
  color: var(--primary);
}
.tf-btn-default.style-white {
  color: var(--white);
}
.tf-btn-default.style-white:hover {
  color: var(--primary);
}

.btn-style-1 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 7px 15px;
  border: 1px solid var(--line);
  border-radius: 999px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  height: 36px;
}

.btn-style-2 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: max-content;
  padding: 14px 15px;
  border-radius: 999px;
  background-color: var(--main);
  color: var(--white);
  letter-spacing: 0.1em;
  border: 0;
}
.btn-style-2:hover {
  background-color: var(--primary);
  color: var(--white);
}
.btn-style-2.btn-lg {
  padding: 16px 40px;
}
.btn-style-2.btn-lg-2 {
  padding: 15px;
}

.btn-style-3 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 15px;
  border-radius: 999px;
  background-color: var(--primary);
  color: var(--white);
  letter-spacing: 0.1em;
}
.btn-style-3:hover {
  background-color: var(--main);
}

.btn-style-4 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: max-content;
  padding: 15px 39px;
  border-radius: 4px;
  border: 1px solid var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.btn-style-4:hover {
  background-color: var(--main);
  color: var(--white);
}

.btn-style-5 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: max-content;
  padding: 15px 39px;
  border-radius: 4px;
  border: 1px solid rgb(254, 167, 36);
  background-color: rgb(254, 167, 36);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.btn-style-5:hover {
  background-color: var(--white);
  border-color: var(--main);
  color: var(--main);
}

.btn-style-6 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: max-content;
  padding: 12px 24px;
  border-radius: 99px;
  background-color: var(--white);
  border: 0;
  color: var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.btn-style-6 .icon {
  font-size: 24px;
}
.btn-style-6:hover {
  background-color: var(--main);
  color: var(--white);
}

.btn-sold-out {
  pointer-events: none;
  background-color: var(--line) !important;
  color: var(--secondary-2) !important;
  cursor: no-drop;
}

.btn-out-line {
  padding: 10px 40px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 44px;
  border: 2px solid var(--line);
  background-color: var(--white);
  color: var(--main);
}
.btn-out-line:hover {
  color: var(--primary);
}

.load-more-btn {
  width: auto;
  min-width: 163px;
  height: 42px;
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
}

.tf-loading {
  position: relative;
}
.tf-loading::before {
  width: 18px;
  height: 18px;
  border: solid 2px transparent;
  border-top-color: transparent !important;
  content: "";
  position: absolute;
  z-index: 2;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 50%;
  animation: tf_rotator 0.6s linear infinite paused;
  opacity: 0;
}
.tf-loading .text-btn {
  color: inherit;
}
.tf-loading.loading::before {
  border: solid 2px var(--main);
  opacity: 1;
  animation-play-state: running;
}
.tf-loading.loading .text-btn {
  display: none;
}

.btn-infinite-scroll {
  height: 32px;
  padding: 0;
  border: none;
}
.btn-infinite-scroll::before {
  width: 22px;
  height: 22px;
  border: solid 2px var(--main);
  opacity: 1;
  animation-play-state: running;
}

@keyframes tf_rotator {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.btn-fixed-cart {
  position: fixed;
  top: 40%;
  right: 20px;
  background-color: var(--main);
  width: 60px;
  height: 60px;
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  border-radius: 99px 0px 99px 99px;
}
.btn-fixed-cart .count-box {
  right: 6px;
  top: 16px;
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 99px;
  background-color: var(--primary);
  font-size: 10px;
  line-height: 8px;
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
}

/*------------ form ---------------- */
form {
  position: relative;
}
form .cols {
  display: flex;
  gap: 20px 16px;
  width: 100%;
}
form .cols > * {
  width: 100%;
}

input.style-line-bottom {
  font-family: "DM Sans";
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: var(--main);
  padding: 14px 0 13px !important;
  border: 0;
  border-bottom: 1px solid var(--line);
  border-radius: 0;
}
input.style-line-bottom::placeholder {
  font-family: "DM Sans";
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: #a0a0a0;
}

.form-leave-comment > .wrap {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}
.form-leave-comment button {
  border: 1px solid var(--main);
}

.form-newsletter input {
  font-size: 14px !important;
  height: 56px !important;
  border-radius: 999px;
  border: 1px solid var(--main);
  padding-right: 56px;
}
.form-newsletter button {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: 1px solid var(--main);
}
.form-newsletter.style-black input {
  background-color: var(--main) !important;
  border: 1px solid var(--white) !important;
  color: var(--white) !important;
}
.form-newsletter.style-black button {
  background-color: var(--white);
  color: var(--main);
}
.form-newsletter.style-black button:hover {
  color: var(--white);
  background-color: var(--main);
  border: 1px solid var(--white);
}

.form-search input {
  padding-right: 40px;
  padding-left: 14px;
}
.form-search button {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  background-color: transparent;
  border: 0;
}
.form-search button:hover svg path {
  stroke: var(--primary);
}

.tf-dropdown-sort {
  border-radius: 4px;
  padding: 5px 8px;
  min-width: 100px;
  border: 2px solid var(--line);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-dropdown-sort .icon {
  font-size: 14px;
}
.tf-dropdown-sort .btn-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
}
.tf-dropdown-sort .btn-select {
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  line-height: 22px;
}
.tf-dropdown-sort .dropdown-menu {
  box-shadow: var(--shadow1);
  min-width: 164px;
  border: 0;
  padding: 15px 5px;
  border-radius: 0;
  max-height: 68vh;
  isolation: isolate;
  overscroll-behavior-y: contain;
  overflow-y: auto;
}
.tf-dropdown-sort .dropdown-menu::-webkit-scrollbar {
  width: 5px;
}
.tf-dropdown-sort .dropdown-menu::-webkit-scrollbar-track {
  background-color: var(--bg-scrollbar-track);
}
.tf-dropdown-sort .dropdown-menu::-webkit-scrollbar-thumb {
  background: var(--bg-scrollbar-thumb);
  border-radius: 4px;
}
.tf-dropdown-sort .select-item {
  position: relative;
  font-size: 14px;
  font-weight: 500;
  color: var(--secondary);
  padding: 0 15px;
  line-height: 30px;
  width: 100%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-dropdown-sort .select-item.active {
  background-color: var(--line);
  color: var(--main);
  padding: 0 15px !important;
  border: 0 !important;
}
.tf-dropdown-sort .select-item:hover {
  background-color: var(--line);
  color: var(--main);
}
.tf-dropdown-sort:hover {
  border-color: var(--main);
}
.tf-dropdown-sort.full .dropdown-menu {
  width: 100%;
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.tf-dropdown-sort.style-1 {
  padding: 10px 14px;
  border-radius: 8px;
  border: 2px solid var(--line);
}
.tf-dropdown-sort.has-color .select-item {
  display: flex;
  gap: 8px;
  align-items: center;
}
.tf-dropdown-sort.has-color .box-color {
  width: 20px;
  height: 20px;
}
.tf-dropdown-sort.style-2 {
  min-width: unset;
  padding: 9px 11px;
  border-radius: 12px;
  background-color: var(--surface);
}
.tf-dropdown-sort.style-2 .btn-select {
  gap: 16px;
}
.tf-dropdown-sort.style-2 .btn-select .text-sort-value {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0.1em;
}

.form-search-select {
  display: flex;
  min-width: 610px;
  border-radius: 4px;
  overflow: hidden;
}
.form-search-select .tf-dropdown-sort {
  border-radius: 0;
  flex-shrink: 0;
  min-width: 120px;
  border: none;
  background-color: var(--surface);
  padding: 8px 12px;
  font-weight: 600;
}
.form-search-select .tf-dropdown-sort .icon {
  font-size: 20px;
}
.form-search-select .tf-dropdown-sort .btn-select {
  font-size: 16px;
  line-height: 26px;
}
.form-search-select input {
  border-color: transparent !important;
  border-radius: 0;
  padding: 6px 16px;
}
.form-search-select .tf-btn {
  flex-shrink: 0;
  padding: 7px 28px;
  border-radius: 0;
}

.form-write-review .heading {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
  margin-bottom: 24px;
}
.form-write-review textarea {
  height: 100px;
}
.form-write-review button {
  border: 1px solid var(--main);
  padding: 15px 39px;
  letter-spacing: 0.1em;
}

.list-rating-check {
  display: flex;
  flex-direction: row-reverse;
  justify-content: left;
  gap: 10px;
  position: relative;
}

.list-rating-check:not(:checked) > input {
  position: absolute;
  opacity: 0;
  visibility: hidden;
  width: 0;
}

.list-rating-check:not(:checked) > label {
  font-size: 40px;
  cursor: pointer;
  white-space: nowrap;
  width: 40px;
  color: var(--line);
}

.list-rating-check:not(:checked) > label:before {
  font-family: "icomoon";
  content: "\e92a";
}

.list-rating-check > input:checked ~ label {
  color: var(--yellow);
}

.list-rating-check:not(:checked) > label:hover,
.list-rating-check:not(:checked) > label:hover ~ label {
  color: var(--yellow);
}

.list-rating-check > input:checked + label:hover,
.list-rating-check > input:checked + label:hover ~ label,
.list-rating-check > input:checked ~ label:hover,
.list-rating-check > input:checked ~ label:hover ~ label,
.list-rating-check > label:hover ~ input:checked ~ label {
  color: var(--yellow);
}

.tf-check {
  position: relative;
  background: transparent;
  cursor: pointer;
  outline: 0;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  min-width: 20px;
  border: 1px solid var(--secondary-2);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
}
.tf-check:checked {
  border-color: var(--main);
  background-color: var(--main);
}
.tf-check:checked::before {
  opacity: 1;
  transform: scale(1);
}
.tf-check::before {
  font-weight: 500;
  font-family: "icomoon";
  content: "\e937";
  position: absolute;
  color: var(--white);
  opacity: 0;
  font-size: 12px;
  transform: scale(0);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.tf-check-rounded {
  position: relative;
  border: 1px solid var(--secondary-2);
  border-radius: 50%;
  background: none;
  cursor: pointer;
  outline: 0;
  height: 14px;
  width: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-appearance: none;
}
.tf-check-rounded::before {
  content: "";
  position: absolute;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  background-color: var(--main);
  opacity: 0;
}
.tf-check-rounded:checked {
  border-color: var(--main);
}
.tf-check-rounded:checked::before {
  opacity: 1;
}

.tf-select {
  position: relative;
}
.tf-select select {
  width: 100%;
  padding: 7px 16px;
  border: 2px solid var(--line);
  border-radius: 999px;
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-select::after {
  font-family: "icomoon";
  position: absolute;
  content: "\e935";
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  z-index: -1;
}
.tf-select:hover select {
  border-color: var(--main);
}

.form-bundle-product {
  padding: 23px;
  border-radius: 12px;
  border: 1px solid var(--line);
  display: flex;
  gap: 16px;
  flex-direction: column;
}
.form-bundle-product .tf-bundle-product-total-submit {
  display: flex;
  gap: 12px;
  align-items: center;
}
.form-bundle-product .tf-bundle-product-btn {
  padding: 14px 48px;
}
.form-bundle-product.type-cols .tf-bundle-products-wrap {
  display: flex;
  gap: 16px 24px;
  overflow-x: auto;
}
.form-bundle-product.type-cols .tf-bundle-products-wrap::-webkit-scrollbar {
  height: 8px;
}
.form-bundle-product.type-cols .tf-bundle-products-wrap::-webkit-scrollbar-thumb {
  background: var(--secondary-2);
}
.form-bundle-product.type-cols .tf-bundle-products-wrap::-webkit-scrollbar-track {
  background: var(--line);
}
.form-bundle-product.type-cols .tf-bundle-product-item {
  width: 173px;
  flex-shrink: 0;
  flex-direction: column;
}
.form-bundle-product.type-cols .tf-bundle-product-item .tf-product-bundle-infos {
  gap: 7px;
}
.form-bundle-product.type-cols .tf-bundle-product-item .tf-product-bundle-image {
  width: 100%;
  height: 226px;
}
.form-bundle-product.type-product-grouped {
  padding: 0;
  border: 0;
  border-radius: 0;
  gap: 20px;
}
.form-bundle-product.type-product-grouped .tf-bundle-product-item {
  padding-bottom: 20px;
  border-bottom: 1px solid var(--line);
}

.form-login .wrap {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 28px;
}
.form-login .wrap .forget-password {
  text-decoration: underline;
}
.form-login .tf-cart-checkbox .tf-checkbox-wrapp {
  width: 20px;
  height: 20px;
  gap: 8px;
}
.form-login .tf-cart-checkbox .tf-checkbox-wrapp input,
.form-login .tf-cart-checkbox .tf-checkbox-wrapp div {
  width: 20px;
  height: 20px;
  min-width: 20px;
  border-radius: 3px;
}
.form-login button {
  padding: 10px 32px;
}

.form-has-password .toggle-password {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: inline-flex;
  cursor: pointer;
}
.form-has-password .toggle-password i {
  font-size: 20px;
  color: var(--secondary-2);
}
.form-has-password .toggle-password:not(.unshow) i::before {
  content: "\e938";
}

.account-address .tf-btn {
  cursor: pointer;
  padding: 10px 32px;
}

.wd-form-address {
  margin: 20px 0px 40px;
  border-radius: 10px;
  padding: 20px 15px;
  border: 1px solid var(--line);
}
.wd-form-address .title {
  font-size: 28px;
  line-height: 33.6px;
  margin: 20px 0px;
}
.wd-form-address .box-field {
  margin-bottom: 15px;
}
.wd-form-address .tf-select select {
  border-radius: 8px;
  height: 46px;
}

.contact-wrap-form {
  border-radius: 16px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  gap: 20px;
}
.contact-wrap-form .box-title {
  display: grid;
  gap: 10px;
}

.form-newsletter-subscribe-2 input {
  border-radius: 128px;
  padding: 14px 30px;
  background: transparent;
  border: 1px solid #a0a0a0;
  text-align: center;
}
.form-newsletter-subscribe-2 #subscribe-content {
  display: grid;
  gap: 10px;
}

/*------------ nice select ---------------- */
.nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border: 0;
  padding: 0;
  padding-right: 16px;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: flex;
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
  outline: none;
  position: relative;
  transition: all linear 0.2s;
  user-select: none;
  white-space: nowrap;
  width: max-content;
  border-radius: 0;
  color: var(--main);
}

.nice-select:active,
.nice-select.open,
.nice-select:focus {
  border-color: var(--line);
}

.nice-select:after {
  border-bottom: 1.7px solid var(--main);
  border-right: 1.7px solid var(--main);
  content: "";
  height: 8px;
  width: 8px;
  margin-top: -6px;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform-origin: 66% 66%;
  -ms-transform-origin: 66% 66%;
  transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.nice-select.open:after {
  -webkit-transform: rotate(-135deg);
  -ms-transform: rotate(-135deg);
  transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
}

.nice-select.open .list {
  opacity: 1;
  z-index: 10;
  pointer-events: auto;
  -webkit-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  -moz-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
}

.nice-select.disabled {
  border-color: #ededed;
  color: #999;
  pointer-events: none;
}

.nice-select.disabled:after {
  border-color: #cccccc;
}

.nice-select.wide {
  width: 100%;
}

.nice-select.wide .list {
  left: 0 !important;
  right: 0 !important;
}

.nice-select.right {
  float: right;
}

.nice-select.right .list {
  left: auto;
  right: 0;
}

.nice-select.small {
  font-size: 12px;
  height: 36px;
  line-height: 34px;
}

.nice-select.small:after {
  height: 4px;
  width: 4px;
}

.nice-select.small .option {
  line-height: 34px;
  min-height: 34px;
}

.nice-select .list {
  background-color: var(--white);
  border-radius: 5px;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  box-sizing: border-box;
  margin-top: 4px;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  -webkit-transform-origin: 50% 0;
  -ms-transform-origin: 50% 0;
  transform-origin: 50% 0;
  -webkit-transform: scale(0.75) translateY(-21px);
  -ms-transform: scale(0.75) translateY(-21px);
  transform: scale(0.75) translateY(-21px);
  -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 9;
  width: 100%;
  font-size: 14px;
  max-height: 155px;
  overflow: auto;
}

.nice-select .list.style {
  max-height: unset;
}

.nice-select .list::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .list::-webkit-scrollbar-thumb {
  background-color: #a7a7a7;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .list::-webkit-scrollbar {
  width: 6px;
  height: 4px;
  background-color: #f5f5f5;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .option {
  cursor: pointer;
  font-weight: 500;
  line-height: 40px;
  list-style: none;
  min-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  font-size: 16px;
  text-align: left;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  color: var(--main);
}

.nice-select .option:hover,
.nice-select .option.focus,
.nice-select .option.selected.focus {
  background-color: var(--white);
  color: var(--primary);
}

.nice-select .option.selected {
  font-weight: 600;
}

.nice-select .option.disabled {
  color: var(--main);
  cursor: default;
}

.no-csspointerevents .nice-select .list {
  display: none;
}

.no-csspointerevents .nice-select.open .list {
  display: block;
}

.image-select.style-default {
  width: unset !important;
  display: flex;
}
.image-select.style-default > select {
  display: none !important;
}
.image-select.style-default > .dropdown-toggle {
  padding: 0;
  padding-right: 20px;
  background-color: transparent !important;
  border: 0 !important;
  outline: none !important;
  color: var(--main);
}
.image-select.style-default > .dropdown-toggle::after {
  border: 0;
  position: absolute;
  right: 0;
  content: "\e935";
  font-family: "icomoon";
  font-size: 12px;
  color: var(--main);
}
.image-select.style-default .filter-option-inner-inner {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
.image-select.style-default .filter-option-inner-inner img {
  width: 20px;
  height: 15px;
}
.image-select.style-default > .dropdown-menu {
  overflow: unset !important;
  margin-top: 17px !important;
  margin-bottom: 17px !important;
  padding: 15px 20px;
  border-radius: 0;
  border: 0;
  background-color: var(--white);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 18px 0px;
}
.image-select.style-default > .dropdown-menu a {
  padding: 5px 0;
}
.image-select.style-default > .dropdown-menu a .text {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 5px;
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
}
.image-select.style-default > .dropdown-menu a .text img {
  width: 16px;
  height: 12px;
}
.image-select.style-default > .dropdown-menu a:hover, .image-select.style-default > .dropdown-menu a:active, .image-select.style-default > .dropdown-menu a.active {
  color: var(--primary) !important;
  background-color: unset !important;
}
.image-select.style-default > .dropdown-menu::after {
  position: absolute;
  content: "";
  width: 16px;
  height: 16px;
  transform: translate(-50%, -50%) rotate(45deg);
  background-color: var(--white);
  top: 0;
  left: 50%;
  z-index: 2;
}
.image-select.style-default > .dropdown-menu[data-popper-placement=top-start]::after {
  display: none;
}
.image-select.style-default > .dropdown-menu[data-popper-placement=top-start]::before {
  position: absolute;
  content: "";
  width: 16px;
  height: 16px;
  transform: translate(-50%, 50%) rotate(45deg);
  background-color: var(--white);
  bottom: 0%;
  left: 50%;
  z-index: 2;
}
.image-select.type-currencies > .dropdown-menu {
  width: 120px !important;
}
.image-select.type-languages > .dropdown-menu {
  width: 96px !important;
}
.image-select.color-secondary-2 > .dropdown-toggle {
  color: var(--secondary-2);
}
.image-select.color-secondary-2 > .dropdown-toggle::after {
  color: var(--secondary-2);
}
.image-select.color-secondary-2 > .dropdown-toggle .filter-option .filter-option-inner {
  color: var(--secondary-2);
}
.image-select.color-white > .dropdown-toggle {
  color: var(--white);
}
.image-select.color-white > .dropdown-toggle::after {
  color: var(--white);
}
.image-select.color-white > .dropdown-toggle .filter-option .filter-option-inner {
  color: var(--white);
}

/*------------ carousel ---------------- */
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0px;
}

.sw-auto .swiper-wrapper {
  align-items: center;
}
.sw-auto .swiper-slide {
  width: auto;
  transition-timing-function: linear;
}

.sw-dots {
  display: flex;
  gap: 8px;
}
.sw-dots.type-circle .swiper-pagination-bullet {
  position: relative;
  width: 20px;
  height: 20px;
  background-color: transparent;
  border: 1px solid transparent;
  opacity: 1;
}
.sw-dots.type-circle .swiper-pagination-bullet::after {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  width: 8px;
  height: 8px;
  background-color: transparent;
  border-radius: 50%;
  border: 1px solid var(--main);
  opacity: 1;
}
.sw-dots.type-circle .swiper-pagination-bullet.swiper-pagination-bullet-active {
  border: 1px solid var(--main);
}
.sw-dots.type-circle .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
  background-color: var(--main);
}
.sw-dots.white-circle .swiper-pagination-bullet::after {
  background-color: var(--white);
  border-color: var(--white);
}
.sw-dots.white-circle .swiper-pagination-bullet.swiper-pagination-bullet-active {
  border: 1px solid var(--white);
}
.sw-dots.white-circle .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
  background-color: var(--white);
}
.sw-dots.white-circle-line .swiper-pagination-bullet::after {
  border-color: var(--white);
}
.sw-dots.white-circle-line .swiper-pagination-bullet.swiper-pagination-bullet-active {
  border: 1px solid var(--white);
}
.sw-dots.white-circle-line .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
  background-color: var(--white);
}
.sw-dots.type-square .swiper-pagination-bullet {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  width: 24px;
  height: 4px;
  border-radius: 99px;
  background-color: var(--main);
  opacity: 0.3;
}
.sw-dots.type-square .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 40px;
  opacity: 1;
}
.sw-dots.white-square .swiper-pagination-bullet {
  background-color: var(--white);
}
.sw-dots:not(.swiper-pagination-lock) {
  margin-top: 20px;
}

.nav-sw {
  background-color: var(--white);
  border-radius: 999px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--secondary);
  color: var(--main);
  cursor: pointer;
}
.nav-sw .icon {
  font-size: 18px;
}
.nav-sw:hover {
  background-color: var(--main);
  border-color: var(--main);
  color: var(--white);
}
.nav-sw.lg {
  width: 40px;
  height: 40px;
  border-color: transparent;
}
.nav-sw.lg .icon {
  font-size: 20px;
}
.nav-sw.swiper-button-disabled {
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--main);
  border-color: transparent;
  cursor: text;
}

.slider-auto-vertical .swiper-slide {
  height: max-content !important;
}

.layout-sw-center {
  overflow: hidden;
}

.swiper .sec-btn {
  margin-top: 20px;
}

.flat-sw-navigation {
  position: relative;
}
.flat-sw-navigation .nav-sw {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}
.flat-sw-navigation .nav-sw.nav-sw-left {
  left: 20px;
}
.flat-sw-navigation .nav-sw.nav-sw-right {
  right: 20px;
}

.flat-sw-pagination {
  position: relative;
}
.flat-sw-pagination .sw-dots {
  position: absolute;
  margin-top: 0;
  z-index: 10;
  bottom: 20px;
}

/*------------ avatar ---------------- */
.avatar.round {
  border-radius: 50%;
  overflow: hidden;
}
.avatar img {
  width: 100%;
  min-width: 100%;
  height: 100%;
  object-fit: cover;
}

.avt-40 {
  width: 40px;
  min-width: 40px;
  height: 40px;
}

.avt-56 {
  width: 56px;
  min-width: 56px;
  height: 56px;
}

.avt-60 {
  width: 60px;
  min-width: 60px;
  height: 60px;
}

.avt-100 {
  width: 100px;
  min-width: 100px;
  height: 100px;
}

/*------------ pop up ---------------- */
.offcanvas {
  z-index: 3000;
  border: none !important;
}
.offcanvas .icon-close-popup {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.offcanvas .icon-close-popup:hover {
  color: var(--primary);
  transform: rotate(90deg);
}

.offcanvas-backdrop {
  background-color: var(--backdrop);
  cursor: url(../images/cursor-close.svg), auto;
}
.offcanvas-backdrop.show {
  opacity: 1;
}

.overflow-x-auto::-webkit-scrollbar,
.overflow-y-auto::-webkit-scrollbar {
  width: 0px;
}

.modal-backdrop {
  background-color: var(--backdrop);
}
.modal-backdrop.show {
  opacity: 1;
}

.modal {
  cursor: url(../images/cursor-close.svg), auto;
}
.modal .icon-close,
.modal .icon-close-popup {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.modal .icon-close:hover,
.modal .icon-close-popup:hover {
  color: var(--primary);
  transform: rotate(90deg);
}
.modal.fullRight .modal-dialog {
  transform: translate(100%, 0);
  min-width: 100%;
  height: 100%;
  margin: 0;
  transition: transform 1s ease-out;
}
.modal.fullRight .modal-dialog .modal-content {
  border-radius: 0;
  border: 0;
  margin: auto;
  overflow: hidden;
  position: absolute;
  right: 0;
  bottom: 0;
  top: 0;
  padding: 0;
}
.modal.fullRight .modal-dialog .modal-content .modal-body {
  overflow: auto;
  padding: 0;
  padding-bottom: 30px;
}
.modal.fullRight.show .modal-dialog {
  transform: none;
  transition: transform 0.4s ease-out;
}
.modal.fullLeft .modal-dialog {
  transform: translate(-100%, 0) !important;
  min-width: 100%;
  height: 100%;
  margin: 0;
  transition: all 0.3s !important;
}
.modal.fullLeft .modal-dialog .modal-content {
  border-radius: 0;
  border: 0;
  margin: auto;
  overflow: hidden;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  padding: 0;
}
.modal.fullLeft .modal-dialog .modal-content .modal-body {
  overflow: auto;
  padding: 0;
  padding-bottom: 30px;
}
.modal.fullLeft.show .modal-dialog {
  transform: translate(0, 0) !important;
}
.modal.fullBottom .modal-dialog {
  transform: translate(0, 100%);
  min-width: 100%;
  height: 100%;
  max-height: unset;
  margin: 0;
  transition: transform 0.3s linear !important;
}
.modal.fullBottom .modal-dialog .modal-content {
  border-radius: 0;
  border: 0;
  margin: auto;
  overflow: hidden;
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 0;
  max-height: max-content;
}
.modal.fullBottom .modal-dialog .modal-content .modal-body {
  overflow: auto;
  padding: 0;
  padding-bottom: 30px;
}
.modal.fullBottom.show .modal-dialog {
  transform: translate(0, 0);
}
.modal.modalCentered .modal-dialog {
  transform: translate(0, 0) !important;
}
.modal.fade:not(.show) {
  opacity: 0;
}
.modal .modal-content {
  cursor: default !important;
}

.modalDemo .demo-title {
  margin-top: 50px;
  margin-bottom: 44px;
  font-weight: 500;
  text-align: center;
}
.modalDemo .modal-dialog {
  max-width: 1540px;
  margin-top: 8px;
  margin-bottom: 8px;
  height: calc(100vh - 16px);
}
.modalDemo .modal-content {
  padding: 32px 0px;
  background-color: var(--white);
  width: 100%;
  border-radius: 20px;
  margin: 0 30px;
  max-height: calc(100vh - 60px);
  border: 0;
  cursor: default;
  overflow: hidden;
}
.modalDemo .mega-menu {
  padding: 0 32px;
  overscroll-behavior-y: contain;
  overflow-y: auto;
}
.modalDemo .mega-menu::-webkit-scrollbar {
  width: 6px;
}
.modalDemo .mega-menu::-webkit-scrollbar:hover {
  width: 12px;
  height: 12px;
}
.modalDemo .mega-menu::-webkit-scrollbar-thumb {
  background: var(--line);
  transition: all 0.3s ease;
}
.modalDemo .header {
  position: relative;
}
.modalDemo .header .icon-close-popup {
  position: absolute;
  top: 18px;
  right: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: rgb(134, 134, 134);
}

.tf-product-modal .modal-dialog {
  max-width: min(625px, 90vw);
}
.tf-product-modal .modal-dialog .modal-content {
  padding: 38px 36px 40px;
  margin-left: 0;
  margin-right: 0;
  border: 0;
}
.tf-product-modal .modal-dialog .modal-content .header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tf-product-modal .modal-dialog .modal-content .header .demo-title {
  margin: 0;
  text-align: start;
  font-size: 26px;
  font-weight: 400;
  line-height: 31px;
}
.tf-product-modal .modal-dialog .modal-content .header span {
  position: unset;
  color: var(--main);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-product-modal .modal-dialog .modal-content .header span:hover {
  color: var(--primary);
}
.tf-product-modal .modal-dialog .modal-content h6 {
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 25px;
}
.tf-product-modal .modal-dialog .modal-content .title {
  font-size: 18px;
  font-weight: 600;
  line-height: 22px;
  margin-top: 15px;
}
.tf-product-modal .modal-dialog .modal-content p {
  margin-top: 15px;
  margin-bottom: 20px;
}
.tf-product-modal .tf-social-icon .box-icon {
  width: 40px;
  height: 40px;
  border-radius: 999px;
  font-size: 16px;
  color: var(--white);
}
.tf-product-modal .tf-social-icon .box-icon.social-twiter {
  font-size: 12px;
  background: var(--twitter-cl);
}
.tf-product-modal .tf-social-icon .box-icon.social-facebook {
  background: var(--facebook-cl);
}
.tf-product-modal .tf-social-icon .box-icon.social-instagram {
  background: var(--instagram-cl);
}
.tf-product-modal .tf-social-icon .box-icon.social-tiktok {
  background: var(--tiktok-cl);
}
.tf-product-modal .tf-social-icon .box-icon.social-pinterest {
  background: var(--pinterest-cl);
}
.tf-product-modal .form-share {
  margin-top: 20px;
  position: relative;
}
.tf-product-modal .form-share .button-submit {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0px;
}
.tf-product-modal .form-share .button-submit .tf-btn {
  padding: 10px 18px;
}
.tf-product-modal .form-share input {
  padding-right: 80px;
}

#ask_question fieldset {
  margin-bottom: 15px;
}
#ask_question fieldset label {
  margin-bottom: 5px;
  font-weight: 400;
  color: var(--text);
}
#ask_question fieldset input {
  height: 50px;
}
#ask_question textarea {
  height: 176px;
}
#ask_question button {
  border-radius: 4px;
}

#delivery_return .tf-product-popup-delivery .title {
  font-size: 20px;
  font-weight: 400;
  line-height: 36px;
  margin-bottom: 25px;
  margin-top: 0;
}
#delivery_return .tf-product-popup-delivery p {
  color: rgb(134, 134, 134);
  line-height: 22.4px;
  margin-bottom: 10px;
}
#delivery_return .tf-product-popup-delivery p a {
  color: rgb(134, 134, 134);
  text-decoration: underline;
  text-underline-offset: 3px;
}
#delivery_return .tf-product-popup-delivery p a:hover {
  color: var(--main);
  text-decoration-thickness: 2px;
}
#delivery_return .tf-product-popup-delivery:not(:last-child) {
  margin-bottom: 20px;
}

#quick_add .modal-dialog {
  max-width: min(466px, 90vw);
}
#quick_add .modal-content {
  margin: 8px;
  padding: 30px 0px 30px;
}
#quick_add .modal-content > .wrap {
  overflow-y: auto;
  padding: 0px 20px;
}
#quick_add .modal-content > .wrap::-webkit-scrollbar {
  width: 2px;
}
#quick_add .modal-content .icon-close-popup {
  top: 0px;
  right: 20px;
}
#quick_add .tf-product-info-item {
  margin-bottom: 15px;
  display: flex;
  gap: 18px;
  align-items: center;
}
#quick_add .tf-product-info-item .image img {
  width: 70px;
  height: 98px;
}
#quick_add .tf-product-info-item .content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
#quick_add .tf-product-info-item .content a {
  font-size: 20px;
  line-height: 24px;
}
#quick_add .tf-product-info-item .content .price {
  font-size: 20px;
  line-height: 20px;
}
#quick_add .payment-more-option {
  text-decoration: none;
}
#quick_add .payment-more-option:hover {
  text-decoration: underline;
}

.bg-color-beige {
  background: conic-gradient(#c8ad7f 0deg 360deg);
}

.bg-color-black {
  background: conic-gradient(#000000 0deg 360deg);
}

.bg-color-blue {
  background: conic-gradient(#a8bcd4 0deg 360deg);
}

.bg-color-white {
  background: conic-gradient(#ffffff 0deg 360deg);
}

.bg-color-pink {
  background: conic-gradient(#fcc6de 0deg 360deg);
}

.bg-color-brown {
  background: conic-gradient(#977945 0deg 360deg);
}

.bg-color-light-purple {
  background: conic-gradient(#d966d9 0deg 360deg);
}

.bg-color-light-green {
  background: conic-gradient(#caffd6 0deg 360deg);
}

.bg-color-orange {
  background: conic-gradient(#ffa500 0deg 360deg);
}

.bg-color-light-blue {
  background: conic-gradient(#add8e6 0deg 360deg);
}

.bg-color-gray {
  background-color: var(--secondary);
}

.bg-color-beige1 {
  background: rgb(223, 198, 184);
}

.bg-color-grey {
  background-color: rgb(158, 155, 150);
}

.bg-color-red {
  background-color: #dc2a35;
}

.canvas-wrapper {
  padding: 0;
  isolation: isolate;
  height: 100%;
  width: 100%;
  max-height: none;
  display: grid;
  grid-auto-rows: auto minmax(0, 1fr) auto;
  align-content: start;
}

.canvas-body {
  background-color: var(--white);
  padding: 15px 20px;
  overscroll-behavior-y: contain;
  overflow-y: auto;
}
.canvas-body::-webkit-scrollbar {
  width: 5px;
}
.canvas-body::-webkit-scrollbar-track {
  background-color: var(--bg-scrollbar-track);
}
.canvas-body::-webkit-scrollbar-thumb {
  background: var(--bg-scrollbar-thumb);
  border-radius: 4px;
}

.canvas-sidebar {
  max-width: min(90vw, 360px);
}
.canvas-sidebar .canvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 15px;
  background-color: var(--surface);
  min-height: 40px;
}
.canvas-sidebar .canvas-header .icon-close-popup {
  font-size: 16px;
}
.canvas-sidebar .canvas-body {
  padding: 15px;
}
.canvas-sidebar .canvas-body .sidebar-account {
  padding: 32px 15px;
  border-radius: 12px;
}
.canvas-sidebar .canvas-body .sidebar-account .account-avatar {
  margin-bottom: 16px;
}
.canvas-sidebar .canvas-body .sidebar-account .my-account-nav .my-account-nav-item {
  padding: 10px;
  border-radius: 8px;
}

.canvas-compare {
  height: max-content !important;
  z-index: 5000;
}
.canvas-compare .close-popup {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
}
.canvas-compare .canvas-body {
  padding: 28px 0;
}

.offcanvas-backdrop {
  z-index: 2000;
}

.modal-shopping-cart .modal-content {
  max-width: 708px !important;
  cursor: default !important;
  display: flex;
  flex-direction: row;
}
.modal-shopping-cart .tf-minicart-recommendations {
  width: 228px;
  flex-shrink: 0;
  padding: 24px 23px 24px 24px;
  border-right: 1px solid var(--line);
  display: flex;
  flex-direction: column;
}
.modal-shopping-cart .tf-minicart-recommendations .title {
  margin-bottom: 12px;
}
.modal-shopping-cart .tf-minicart-recommendations > .wrap-recommendations {
  flex-grow: 1;
  overflow-y: auto;
}
.modal-shopping-cart .tf-minicart-recommendations > .wrap-recommendations::-webkit-scrollbar {
  width: 0px;
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item:not(:last-child) {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--line);
  margin-bottom: 16px;
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item .image {
  width: 100%;
  height: 100%;
  max-height: 240px;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item .name {
  margin-bottom: 4px;
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item .cart-item-bot {
  position: relative;
  overflow: hidden;
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item .cart-item-bot a {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transform: translateY(30px);
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item .price {
  transform: translateY(0px);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item:hover .price {
  transform: translateY(-30px);
}
.modal-shopping-cart .tf-minicart-recommendations .list-cart-item:hover .cart-item-bot a {
  transform: translateY(0);
}
.modal-shopping-cart .header {
  padding: 24px 24px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.modal-shopping-cart .header .title {
  font-size: 20px;
}
.modal-shopping-cart .header .icon-close-popup {
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface);
  border-radius: 50%;
  cursor: pointer;
}
.modal-shopping-cart .wrap {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.modal-shopping-cart .tf-mini-cart-threshold {
  margin: 0 24px;
  padding: 16px;
  background-color: rgb(245, 246, 236);
  border-radius: 12px;
}
.modal-shopping-cart .tf-mini-cart-threshold .tf-progress-bar {
  margin-top: 12px;
  margin-bottom: 16px;
  width: 100%;
  background-color: var(--white);
  height: 8px;
  position: relative;
}
.modal-shopping-cart .tf-mini-cart-threshold .tf-progress-bar div {
  height: 100%;
  background: linear-gradient(90deg, #19450f 0%, #3dab25 100%);
  position: relative;
  transition: width 2s ease;
}
.modal-shopping-cart .tf-mini-cart-threshold .tf-progress-bar .icon {
  position: absolute;
  left: 95%;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border: 2px solid var(--success);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white);
  border-radius: 50%;
  font-size: 20px;
}
.modal-shopping-cart .tf-mini-cart-wrap {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}
.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-main {
  flex: 1 1 auto;
  position: relative;
}
.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow: auto;
}
.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll::-webkit-scrollbar {
  width: 8px;
}
.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll::-webkit-scrollbar-thumb {
  background: var(--secondary-2);
}
.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll::-webkit-scrollbar-track {
  background: var(--line);
}
.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-bottom {
  box-shadow: 5px 5px 18px 5px rgba(64, 72, 87, 0.15);
  flex-shrink: 0;
}
.modal-shopping-cart .tf-mini-cart-item {
  margin: 0 24px;
  padding: 20px 0 19px;
  display: flex;
  align-items: center;
  gap: 24px;
}
.modal-shopping-cart .tf-mini-cart-item:not(:last-child) {
  border-bottom: 1px solid var(--line);
}
.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-image {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}
.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-image img {
  width: 100%;
  height: 100px;
  object-fit: cover;
}
.modal-shopping-cart .tf-mini-cart-item .tf-btn-remove {
  color: var(--critical);
  text-decoration: underline;
  cursor: pointer;
}
.modal-shopping-cart .tf-mini-cart-tool {
  padding: 0 25px;
  height: 58px;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid var(--line);
}
.modal-shopping-cart .tf-mini-cart-tool .tf-mini-cart-tool-btn {
  width: 100%;
  height: 100%;
  cursor: pointer;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.modal-shopping-cart .tf-mini-cart-bottom-wrap {
  padding: 24px 24px 20px;
}
.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-totals-discounts {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-checkbox {
  margin-bottom: 24px;
}
.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-mini-cart-view-checkout {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.modal-wishlist .modal-content {
  max-width: 540px !important;
  cursor: default !important;
}
.modal-wishlist .wrap {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.modal-wishlist .header {
  padding: 24px 24px 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.modal-wishlist .tf-mini-cart-wrap {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}
.modal-wishlist .tf-mini-cart-wrap .tf-mini-cart-main {
  flex: 1 1 auto;
  position: relative;
}
.modal-wishlist .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow: auto;
}
.modal-wishlist .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll::-webkit-scrollbar {
  width: 8px;
}
.modal-wishlist .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll::-webkit-scrollbar-thumb {
  background: var(--secondary-2);
}
.modal-wishlist .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll::-webkit-scrollbar-track {
  background: var(--line);
}
.modal-wishlist .tf-mini-cart-item {
  margin: 0 24px;
  padding: 20px 0 19px;
  display: flex;
  align-items: center;
  gap: 24px;
}
.modal-wishlist .tf-mini-cart-item:not(:last-child) {
  border-bottom: 1px solid var(--line);
}
.modal-wishlist .tf-mini-cart-item .tf-mini-cart-image {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}
.modal-wishlist .tf-mini-cart-item .tf-mini-cart-image img {
  width: 100%;
  height: 100px;
  object-fit: cover;
}
.modal-wishlist .tf-mini-cart-item .tf-btn-remove {
  color: var(--critical);
  text-decoration: underline;
  cursor: pointer;
}
.modal-wishlist .tf-mini-cart-bottom {
  box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.1490196078);
  padding: 32px 24px 20px;
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: 16px;
}
.modal-wishlist .tf-mini-cart-bottom .view-all-wishlist {
  border-radius: 4px;
  padding: 16px;
}

.tf-cart-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.tf-cart-checkbox .tf-checkbox-wrapp {
  min-width: 1.6rem;
  place-items: center;
  position: relative;
  overflow: hidden;
  display: flex;
}
.tf-cart-checkbox .tf-checkbox-wrapp input {
  cursor: pointer;
  display: block;
  width: 18px;
  height: 18px;
  transition: 0.2s ease-in-out;
  background-color: var(--white);
  opacity: 0;
}
.tf-cart-checkbox .tf-checkbox-wrapp input:checked + div {
  background-color: var(--main);
}
.tf-cart-checkbox .tf-checkbox-wrapp input:checked + div i {
  transform: scale(1);
}
.tf-cart-checkbox .tf-checkbox-wrapp div {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  transition: 0.25s ease-in-out;
  z-index: 5;
  border: 1px solid var(--line);
  background-color: var(--white);
  color: var(--white);
  pointer-events: none;
}
.tf-cart-checkbox .tf-checkbox-wrapp div i {
  font-size: 11px;
  transform: scale(0);
}
.tf-cart-checkbox label {
  font-weight: 400;
  cursor: pointer;
}
.tf-cart-checkbox label a {
  text-decoration: underline;
  text-underline-offset: 2px;
}
.tf-cart-checkbox .wrap-content {
  display: none;
}
.tf-cart-checkbox.check .wrap-content {
  display: block;
}

.tf-mini-cart-tool-openable {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transform: translateY(100%);
  transition: transform 0.25s ease-in-out;
  z-index: 70;
}
.tf-mini-cart-tool-openable.open {
  box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);
  transform: translateY(0);
}
.tf-mini-cart-tool-openable.open > .overplay {
  opacity: 1;
  visibility: visible;
}
.tf-mini-cart-tool-openable .tf-mini-cart-tool-close {
  cursor: pointer;
}
.tf-mini-cart-tool-openable .tf-btn {
  height: 52px;
}
.tf-mini-cart-tool-openable .tf-mini-cart-tool-content {
  position: relative;
  z-index: 80;
  background-color: var(--white);
}
.tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-mini-cart-tool-text {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 18px 32px 17px;
  border-bottom: 1px solid var(--line);
}
.tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-mini-cart-tool-text .icon {
  display: flex;
}
.tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-mini-cart-tool-wrap {
  padding: 16px 24px 20px;
}
.tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-cart-tool-btns {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-top: 16px;
}
.tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-cart-tool-btns button {
  border-radius: 4px;
}
.tf-mini-cart-tool-openable.add-note textarea {
  background-color: var(--line);
  color: var(--main);
}
.tf-mini-cart-tool-openable.add-note textarea::placeholder {
  color: var(--main);
}
.tf-mini-cart-tool-openable.add-gift .tf-mini-cart-tool-text {
  gap: 20px;
  align-items: start;
}
.tf-mini-cart-tool-openable.add-gift .tf-mini-cart-tool-text .icon {
  border: 1px solid var(--main);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  font-size: 24px;
  background-color: var(--white);
  color: var(--main);
}
.tf-mini-cart-tool-openable.add-gift .tf-gift-wrap-infos p {
  font-size: 20px;
  line-height: 32px;
}

.modal-size-guide .modal-dialog {
  max-width: 1157px;
}
.modal-size-guide .modal-content {
  border: 0;
  padding: 40px;
}
.modal-size-guide .modal-content .header {
  width: max-content;
  margin-bottom: 32px;
}
.modal-size-guide .modal-content .header .icon-close-popup {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface);
  border-radius: 50%;
  cursor: pointer;
}
.modal-size-guide .modal-content .wrap {
  min-height: 325px;
}

.widget-size {
  display: flex;
  gap: 32px;
  align-items: center;
}
.widget-size .box-title-size {
  display: flex;
  align-items: center;
}
.widget-size .box-title-size .title-size {
  width: 92px;
}
.widget-size .box-title-size .number-size {
  width: 80px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 11px;
  border: 1px solid var(--line);
  border-radius: 8px;
}
.widget-size .tow-bar-block {
  position: relative;
  background: var(--line);
  height: 9px;
  border-radius: 0px;
}
.widget-size .tow-bar-block .progress-size {
  position: absolute;
  height: 9px;
  background: var(--main);
  left: 0;
}
.widget-size .range-input {
  position: relative;
  flex-grow: 1;
}
.widget-size .range-input input {
  position: absolute;
  top: 0;
  height: 9px;
  width: 100%;
  background: none;
  outline: none;
  border: none;
  pointer-events: none;
  appearance: none;
}
.widget-size .range-input input::-webkit-slider-thumb {
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 3px solid var(--main);
  outline: none;
  pointer-events: auto;
  -webkit-appearance: none;
  background: var(--white);
}

.canvas-search {
  width: 100% !important;
  max-width: 463px;
  padding-top: 55px;
  border: 0 !important;
}
.canvas-search .tf-search-head {
  padding: 0 22px;
  border-bottom: 1px solid var(--line);
  margin-bottom: 22px;
  box-shadow: unset;
}
.canvas-search .tf-search-head .title {
  font-size: 28px;
  line-height: 34px;
  margin-bottom: 19px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.canvas-search .tf-search-head .close {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
.canvas-search .tf-search-head .tf-search-sticky {
  margin-bottom: 30px;
}
.canvas-search .tf-search-content {
  padding: 0 22px 16px 22px;
}
.canvas-search .tf-search-content-title {
  font-size: 20px;
  line-height: 24px;
  margin-bottom: 30px;
}
.canvas-search .tf-col-quicklink {
  margin-bottom: 32px;
}
.canvas-search .tf-col-quicklink .tf-search-content-title {
  margin-bottom: 14px;
}
.canvas-search .tf-col-quicklink .tf-quicklink-item a {
  padding: 4px 0;
  line-height: 22.4px;
}
.canvas-search .tf-search-hidden-inner {
  padding-top: 5px;
}
.canvas-search .tf-loop-item {
  display: flex;
  gap: 19px;
  align-items: flex-start;
}
.canvas-search .tf-loop-item:not(:last-child) {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--line);
  margin-bottom: 16px;
}
.canvas-search .tf-loop-item .image {
  width: 68px;
  max-height: 95px;
}
.canvas-search .tf-loop-item .tf-product-info-price > div {
  font-size: 14px;
}

.offcanvas-compare {
  height: max-content !important;
}
.offcanvas-compare .offcanvas-content {
  box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);
}
.offcanvas-compare .offcanvas-content .icon-close-popup {
  position: absolute;
  top: 24px;
  right: 24px;
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface);
  border-radius: 50%;
  cursor: pointer;
}
.offcanvas-compare .tf-compare-list {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 24px 0;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap {
  display: flex;
  align-items: center;
  flex-grow: 1;
  overflow: auto hidden;
  gap: 48px;
  padding: 24px 0;
  margin: -24px 0;
  padding-right: 20px;
  margin-right: -20px;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap::-webkit-scrollbar {
  height: 8px;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap::-webkit-scrollbar-thumb {
  background: var(--secondary-2);
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap::-webkit-scrollbar-track {
  background: var(--line);
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item {
  width: 263px;
  flex-shrink: 0;
  position: relative;
  padding: 11px;
  border-radius: 12px;
  border: 1px solid var(--line);
  display: flex;
  align-items: center;
  gap: 16px;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item .image {
  flex-shrink: 0;
  width: 92px;
  height: 123px;
  border-radius: 8px;
  overflow: hidden;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item .text-title {
  margin-bottom: 8px;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item > .icon-close {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--critical);
  border-radius: 50%;
  font-size: 12px;
  color: var(--white);
  z-index: 5;
  cursor: pointer;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item > .btns-repeat {
  position: absolute;
  top: 50%;
  right: -32px;
  transform: translateY(-50%);
  display: flex;
  cursor: pointer;
}
.offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item:last-child > .btns-repeat {
  display: none;
}
.offcanvas-compare .tf-compare-list .tf-compare-buttons {
  display: flex;
  align-items: center;
  justify-content: end;
  width: 220px;
  flex-shrink: 0;
}
.offcanvas-compare .tf-compare-list .tf-compare-buttons a {
  height: 52px;
}
.offcanvas-compare .tf-compare-list .tf-compare-buttons .tf-compapre-button-clear-all {
  margin-top: 16px;
  height: 48px;
  padding: 13px 0;
  cursor: pointer;
}

.modal-quick-view .modal-content {
  max-width: min(856px, 90vw) !important;
  cursor: default !important;
  display: flex;
  flex-direction: row;
  overflow-y: scroll;
  max-height: 100vh;
}
.modal-quick-view .tf-quick-view-image {
  width: 42.525%;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  position: relative;
}
.modal-quick-view .tf-quick-view-image > .wrap-quick-view {
  flex-grow: 1;
  position: relative;
  padding: 24px;
  padding-right: 0;
  overflow: auto;
  height: 100vh;
  direction: rtl;
}
.modal-quick-view .tf-quick-view-image > .wrap-quick-view::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.modal-quick-view .tf-quick-view-image > .wrap-quick-view::-webkit-scrollbar-track {
  background-color: var(--line);
}
.modal-quick-view .tf-quick-view-image > .wrap-quick-view::-webkit-scrollbar-thumb {
  background: var(--secondary-2);
}
.modal-quick-view .tf-quick-view-image .quickView-item {
  max-height: 440px;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
}
.modal-quick-view .tf-quick-view-image .quickView-item:not(:last-child) {
  margin-bottom: 30px;
}
.modal-quick-view .tf-quick-view-image .quickView-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.modal-quick-view .wrap {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  height: 100%;
  padding: 24px 24px 48px;
  overflow-y: auto;
}
.modal-quick-view .wrap::-webkit-scrollbar {
  width: 8px;
  position: absolute;
  left: 0;
}
.modal-quick-view .wrap::-webkit-scrollbar-thumb {
  background: var(--secondary-2);
}
.modal-quick-view .wrap::-webkit-scrollbar-track {
  background: var(--line);
}
.modal-quick-view .wrap .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.modal-quick-view .wrap .header .icon-close-popup {
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface);
  border-radius: 50%;
  cursor: pointer;
}

.modal-quick-add .modal-content {
  cursor: default !important;
  padding: 24px;
  border: 0;
}
.modal-quick-add .modal-content .icon-close-popup {
  position: absolute;
  top: 24px;
  right: 24px;
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface);
  border-radius: 50%;
  cursor: pointer;
}
.modal-quick-add .modal-content .tf-product-info-item {
  margin-bottom: 15px;
  display: flex;
  gap: 18px;
  align-items: center;
}
.modal-quick-add .modal-content .tf-product-info-item .image img {
  width: 80px;
  height: 100px;
}
.modal-quick-add .modal-content .tf-product-info-item .content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tf-mini-search-frm {
  position: relative;
}
.tf-mini-search-frm input {
  height: 42px;
  padding: 12px 20px 12px 44px;
  font-size: 16px;
  line-height: 26px;
  color: var(--main);
}
.tf-mini-search-frm input::placeholder {
  font-size: 16px;
  line-height: 26px;
  color: var(--main);
}
.tf-mini-search-frm button {
  position: absolute;
  left: 14px;
  top: 0;
  font-size: 16px;
  margin: 13px 0;
  background-color: transparent;
  border: 0;
  outline: none;
}

.form-sign-in .modal-dialog {
  max-width: 640px;
}
.form-sign-in .modal-dialog .modal-content {
  border: 0;
  padding: 37px 35px;
  border-radius: 3px;
}
.form-sign-in .header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.form-sign-in .header .demo-title {
  font-size: 28px;
  line-height: 33.6px;
}
.form-sign-in .header .icon-close-popup {
  padding: 0 6px;
  font-size: 16px;
  cursor: pointer;
}
.form-sign-in .tf-login-form form > div {
  margin-top: 15px;
}
.form-sign-in .tf-login-form form .btn-link {
  margin: 10px 0;
  text-decoration: underline !important;
  text-underline-offset: 3px;
  color: var(--text);
}
.form-sign-in .tf-login-form form .btn-link .icon {
  font-size: 8px;
}
.form-sign-in .tf-login-form form .bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
}
.form-sign-in .tf-login-form form .bottom .btn-link {
  color: var(--main);
}

.toolbar-shop-mobile {
  max-width: min(90%, 430px) !important;
}
.toolbar-shop-mobile .mb-canvas-content {
  max-width: 100%;
  padding-top: 70px;
  padding-left: 0;
}
.toolbar-shop-mobile .mb-body {
  padding: 0 20px 0 21px;
}
.toolbar-shop-mobile ul.nav-ul-mb > li {
  padding: 0 !important;
  border: 0 !important;
}
.toolbar-shop-mobile .tf-category-link {
  gap: 16px;
  min-height: 50px !important;
  padding: 4px 0 6px;
  position: relative;
}
.toolbar-shop-mobile .tf-category-link .image {
  width: 34px;
  height: 34px;
  position: relative;
}
.toolbar-shop-mobile .tf-category-link .image::before {
  position: absolute;
  z-index: 1;
  content: "";
  top: -3px;
  bottom: -3px;
  left: -3px;
  right: -3px;
  width: calc(100% + 6px);
  height: calc(100% + 6px);
  border: solid 1px var(--line);
  margin: auto;
  pointer-events: none;
  border-radius: 50%;
}
.toolbar-shop-mobile .tf-category-link .image img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.toolbar-shop-mobile .tf-category-link > span:nth-child(2) {
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  flex-grow: 1;
}
.toolbar-shop-mobile .tf-category-link::after {
  position: absolute;
  bottom: 0;
  content: "";
  height: 1px;
  width: calc(100% - 53px);
  right: 0;
  left: 53px;
  background-color: rgba(0, 0, 0, 0.12);
}
.toolbar-shop-mobile .tf-category-link .btn-open-sub {
  width: 40px;
  height: 40px;
  border-left: 1px solid var(--line);
}
.toolbar-shop-mobile .tf-category-link.current::after {
  display: none;
}
.toolbar-shop-mobile .tf-category-link.has-children:not(.collapsed)::after {
  display: none;
}
.toolbar-shop-mobile .sub-nav-menu {
  margin: 0px 0 17px 50px !important;
  padding: 0 !important;
}
.toolbar-shop-mobile .sub-nav-menu .tf-category-link {
  padding: 4px 0 4px 15px;
  margin-bottom: 1px;
  min-height: 30px !important;
}
.toolbar-shop-mobile .sub-nav-menu .tf-category-link::after {
  display: none;
}
.toolbar-shop-mobile .sub-menu-level-2 {
  margin-left: 65px !important;
}
.toolbar-shop-mobile .mb-bottom {
  min-height: 50px;
  clear: both;
  padding: 15px 26px;
  background-color: rgba(0, 0, 0, 0.05);
}
.toolbar-shop-mobile .mb-bottom a {
  line-height: 13px;
}
.toolbar-shop-mobile .list-cate {
  position: relative;
}
.toolbar-shop-mobile .list-cate.show::after {
  position: absolute;
  bottom: -17px;
  content: "";
  height: 1px;
  width: calc(100% - 53px);
  right: 0;
  left: 53px;
  background-color: rgba(0, 0, 0, 0.12);
}

.canvas-sidebar-blog .canvas-header {
  background-color: var(--white);
  padding: 14px 20px;
}
.canvas-sidebar-blog .canvas-header .title {
  font-size: 16px;
  line-height: 19.2px;
  font-weight: 400;
}
.canvas-sidebar-blog .canvas-body {
  padding: 20px;
}

.modal-search .icon-close-popup {
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface);
  border-radius: 50%;
  cursor: pointer;
}
.modal-search .modal-dialog {
  max-width: 1100px;
}
.modal-search .modal-content {
  margin-right: 15px;
  margin-left: 15px;
  border: 0;
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.modal-search .tf-loading {
  border-radius: 4px;
  border: 1px solid var(--main);
  margin-left: auto;
  margin-right: auto;
  width: 163px;
  height: 52px;
  justify-content: center;
}
.modal-search .tf-loading::before {
  border-color: var(--white);
}
.modal-search .tf-grid-layout {
  overflow-y: auto;
  padding-right: 34px;
  margin-right: -40px;
  min-height: 100px; /* Placeholder để tránh giật */
  transition: height 0.3s ease;
}
.modal-search .tf-grid-layout::-webkit-scrollbar {
  width: 6px;
}
.modal-search .tf-grid-layout::-webkit-scrollbar-thumb {
  background: var(--secondary);
}

.modal-newleter .modal-dialog {
  max-width: 660px !important;
}
.modal-newleter .modal-content {
  border: 0;
}
.modal-newleter .modal-content .modal-top {
  position: relative;
  border-radius: 10px 10px 0px 0px;
  overflow: hidden;
}
.modal-newleter .modal-content .modal-top .icon {
  position: absolute;
  cursor: pointer;
  top: 12px;
  right: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 999px;
  background-color: var(--white);
  font-size: 16px;
}
.modal-newleter .modal-content .modal-bottom {
  border-radius: 0px 0px 10px 10px;
  background-color: var(--white);
  padding: 40px 15px;
  max-width: 397px;
  margin-left: auto;
  margin-right: auto;
}
.modal-newleter .modal-content .modal-bottom p {
  margin-bottom: 8px;
}
.modal-newleter .modal-content .modal-bottom h5 {
  margin-bottom: 28px;
}
.modal-newleter .modal-content form {
  max-width: 320px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 6px;
}
.modal-newleter .modal-content form input {
  margin-bottom: 16px;
  height: 40px;
}
.modal-newleter .modal-content form button {
  height: 40px;
}

.hover-cursor-img .hover-image {
  display: none;
}

.canvas-categories .canvas-header .icon-close {
  font-size: 14px;
}
.canvas-categories .canvas-header .icon-left {
  font-size: 24px;
}
.canvas-categories .wd-facet-categories {
  padding: 12px 0px;
  border-bottom: 1px solid var(--line);
}
.canvas-categories .wd-facet-categories:first-child {
  padding-top: 0;
}
.canvas-categories .wd-facet-categories:last-child {
  border: none;
}
.canvas-categories .wd-facet-categories .avt {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}
.canvas-categories .wd-facet-categories .facet-title {
  display: flex;
  align-items: center;
  gap: 16px;
}
.canvas-categories .wd-facet-categories .facet-title .title {
  flex-grow: 1;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}
.canvas-categories .wd-facet-categories .facet-title .icon {
  font-size: 16px;
}
.canvas-categories .wd-facet-categories .facet-body {
  margin-top: 12px;
  padding: 8px 10px;
}
.canvas-categories .wd-facet-categories .facet-body li .item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.canvas-categories .wd-facet-categories .facet-body li:not(:last-child) .item {
  margin-bottom: 16px;
}

/*------------ box icon ---------------- */
.box-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tf-social-icon {
  --facebook-cl: rgb(59, 89, 152);
  --twitter-cl: rgb(85, 85, 85);
  --instagram-cl: linear-gradient(#8a3ab9, #e95950, #fccc63);
  --threads-cl: rgb(224, 53, 102);
  --youtube-cl: rgb(205, 32, 31);
  --tiktok-cl: linear-gradient(#25f4ee, #000, #fe2c55);
  --tiktok-cl2: rgb(254, 44, 85);
  --pinterest-cl: rgb(203, 32, 39);
  --tumblr-cl: rgb(55, 69, 92);
  --vimeo-cl: rgb(26, 183, 234);
  --snapchat-cl: rgb(255, 221, 0);
  --whatsapp-cl: rgb(0, 230, 118);
  --linked_in-cl: rgb(23, 106, 255);
  --wechat-cl: rgb(26, 173, 24);
  --reddit-cl: rgb(255, 69, 0);
  --line-cl: rgb(0, 195, 77);
  --spotify-cl: rgb(30, 125, 96);
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.tf-social-icon .social-facebook:hover {
  background: var(--facebook-cl);
  color: var(--white);
  border: none;
}
.tf-social-icon .social-twiter:hover {
  background: var(--twitter-cl);
  color: var(--white);
  border: none;
}
.tf-social-icon .social-instagram:hover {
  background: var(--instagram-cl);
  color: var(--white);
  border: none;
}
.tf-social-icon .social-tiktok:hover {
  background: var(--tiktok-cl);
  color: var(--white);
  border: none;
}
.tf-social-icon .social-pinterest:hover {
  background: var(--pinterest-cl);
  color: var(--white);
  border: none;
}
.tf-social-icon .social-amazon:hover {
  background: var(--main);
  color: var(--white);
  border: none;
}
.tf-social-icon .social-youtube:hover {
  background: var(--youtube-cl);
  color: var(--white);
  border: none;
}
.tf-social-icon a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  font-size: 20px;
  color: var(--main);
  border: 1px solid var(--main);
  border-radius: 50%;
}
.tf-social-icon.style-white a {
  color: var(--white);
  border-color: var(--white);
}
.tf-social-icon.style-fill a {
  background-color: var(--line);
  width: 32px;
  height: 32px;
  border: none;
}
.tf-social-icon.style-fill-2 a {
  background-color: transparent;
  color: var(--white);
  border-color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.1019607843);
}
.tf-social-icon.style-fill-2 a:hover {
  border-color: transparent;
}
.tf-social-icon.style-1 a {
  background-color: var(--surface);
  width: 40px;
  height: 40px;
  font-size: 16px;
  border-color: transparent;
}
.tf-social-icon.style-2 a {
  width: 40px;
  height: 40px;
  border: 0;
  color: var(--main);
  background-color: var(--white);
  font-size: 18px;
}
.tf-social-icon.style-default {
  gap: 8px;
}
.tf-social-icon.style-default a {
  border: 0;
  font-size: 15px;
}
.tf-social-icon.style-small a {
  width: 32px;
  height: 32px;
}
.tf-social-icon.style-small a i {
  font-size: 20px;
}

.tf-icon-box {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}
.tf-icon-box .icon-box {
  font-size: 40px;
}
.tf-icon-box .content {
  display: grid;
  gap: 8px;
}
.tf-icon-box.style-2 {
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 12px;
}
.tf-icon-box.style-2 .icon-box {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 1px solid var(--line);
  display: flex;
  align-items: center;
  justify-content: center;
}
.tf-icon-box.style-2.type-column {
  flex-direction: column;
  gap: 22px;
}
.tf-icon-box.style-3 {
  padding-right: 20px;
  gap: 12px;
}
.tf-icon-box.style-3 .icon-box {
  padding: 12px;
}

.slider-icon-box {
  padding: 30px;
  border-radius: 16px;
}

/*------------ hover ---------------- */
.hover-img .img-style {
  overflow: hidden;
}
.hover-img .img-style > img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  -webkit-transition: opacity 0.5s ease, transform 2s cubic-bezier(0, 0, 0.44, 1.18);
  transition: opacity 0.5s ease, transform 2s cubic-bezier(0, 0, 0.44, 1.18);
}
.hover-img:hover .img-style > img {
  -webkit-transform: scale(1.06);
  transform: scale(1.06);
}
.hover-img .img-style2 {
  overflow: hidden;
  border-radius: 10px;
}
.hover-img .img-style2 .img-hv {
  width: 100%;
  object-fit: cover;
  -webkit-transition: all 1s cubic-bezier(0.3, 1, 0.35, 1) 0s;
  transition: all 1s cubic-bezier(0.3, 1, 0.35, 1) 0s;
  transition: transform 500ms ease;
}

.hover-img2 .img-style2 {
  overflow: hidden;
  border-radius: 8px;
}
.hover-img2 .img-style2 .img2 {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.hover-img2:hover .img2 {
  transform: scale(1.1) rotate(3deg);
}

.hover-img3 .img-style3 {
  border-radius: 8px;
  overflow: hidden;
}
.hover-img3 .img-style3 img {
  width: 100%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.hover-img3:hover img {
  transform: scale(1.075);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.pagi2 .swiper-pagination2:hover .box-img .icon-practice,
.swiper-button-next2:hover .box-img .icon-practice,
.swiper-button-prev2:hover .box-img .icon-practice,
.hv-one:hover .box-img .icon-practice {
  opacity: 1;
  z-index: 99;
  top: 50%;
  transition-delay: 0.5s;
}
.pagi2 .swiper-pagination2:hover .img-style::before,
.swiper-button-next2:hover .img-style::before,
.swiper-button-prev2:hover .img-style::before,
.hv-one:hover .img-style::before {
  opacity: 1;
}
.pagi2 .swiper-pagination2 .img-style,
.swiper-button-next2 .img-style,
.swiper-button-prev2 .img-style,
.hv-one .img-style {
  border-radius: 10px;
  overflow: hidden;
}
.pagi2 .swiper-pagination2 .img-style::before,
.swiper-button-next2 .img-style::before,
.swiper-button-prev2 .img-style::before,
.hv-one .img-style::before {
  content: "";
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  position: absolute;
  background: rgba(0, 0, 0, 0.5019607843);
  width: 100%;
  height: 100%;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
  z-index: 99;
  opacity: 0;
  border-radius: 10px;
}
.pagi2 .swiper-pagination2 .img-style.s-one::before,
.swiper-button-next2 .img-style.s-one::before,
.swiper-button-prev2 .img-style.s-one::before,
.hv-one .img-style.s-one::before {
  border-radius: 50%;
}

.hv-one2:hover .img-style2::before {
  opacity: 1;
  visibility: visible;
}
.hv-one2 .img-style2::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  -webkit-transition: all 0.4s ease-out 0s;
  -moz-transition: all 0.4s ease-out 0s;
  -ms-transition: all 0.4s ease-out 0s;
  -o-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
  opacity: 0;
  visibility: hidden;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
  border-radius: 10px;
}

.hv-tool {
  position: relative;
  transition: all 0.3s ease;
}

.hover-tooltip {
  position: relative;
}
.hover-tooltip .tooltip {
  position: absolute;
  white-space: nowrap;
  padding: 0px 8.5px;
  height: 25px;
  border-radius: 2px;
  bottom: calc(100% + 7px);
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  color: var(--white);
  max-width: 250px;
  width: max-content;
  background-color: var(--main);
  transition: transform 0.4s ease 0.2s, opacity 0.4s ease 0.2s;
  z-index: 5;
  font-size: 12px;
  line-height: 22px;
}
.hover-tooltip .tooltip::before {
  content: "";
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  top: 20px;
  position: absolute;
  background: var(--main);
  width: 8px;
  height: 8px;
  z-index: -1;
}
.hover-tooltip:hover .tooltip {
  opacity: 1;
  visibility: visible;
}
.hover-tooltip.tooltip-bot .tooltip {
  top: calc(100% + 5px);
  bottom: unset;
}
.hover-tooltip.tooltip-bot .tooltip::before {
  top: -2px;
}

.hover-overlay {
  position: relative;
}
.hover-overlay::before {
  position: absolute;
  z-index: 2;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  top: 0;
  left: 0;
  transition: 0.4s ease 0.1s;
  opacity: 0;
  visibility: hidden;
}
.hover-overlay:hover::before {
  opacity: 1;
  visibility: visible;
}

/*------------ collection ---------------- */
.collection-default {
  display: grid;
  gap: 20px;
  position: relative;
}
.collection-default .img-style {
  display: block;
  width: 100%;
  height: 100%;
}
.collection-default .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.collection-default .content {
  gap: 8px;
  display: grid;
}
.collection-default.style-1 {
  gap: 0;
}
.collection-default.style-1 .img-style {
  border-radius: 8px 8px 0 0;
  aspect-ratio: 1.7755681818;
}
.collection-default.style-1 .content {
  border-radius: 0 0 8px 8px;
  border: 1px solid var(--line);
  border-top: 0;
  padding: 40px 15px 39px;
}
.collection-default.style-row {
  grid-template-columns: 1fr 1fr;
}
.collection-default.style-row .img-style {
  border-radius: 8px 0 0 8px;
  aspect-ratio: 1.7916666667;
}
.collection-default.style-row .content {
  border-radius: 0 8px 8px 0;
  border-top: 1px solid var(--line);
  border-left: 0;
  display: block;
  place-content: center;
  padding: 48px;
}
.collection-default.style-row .content .title {
  margin-bottom: 12px;
}
.collection-default.style-row .content .desc {
  margin-bottom: 32px;
}
.collection-default.abs-left-center {
  border-radius: 20px;
  overflow: hidden;
}
.collection-default.abs-left-center .content {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: grid;
  gap: 12px;
}
.collection-default.abs-left-center .box-title {
  display: grid;
  gap: 4px;
}
.collection-default.abs-left-bottom {
  overflow: hidden;
  position: relative;
}
.collection-default.abs-left-bottom .content {
  position: absolute;
  left: 20px;
  bottom: 20px;
  gap: 12px;
}
.collection-default.abs-left-bottom .box-title {
  display: grid;
  gap: 8px;
}
.collection-default.abs-left-bottom.type-2 {
  border-radius: 20px;
  overflow: hidden;
}
.collection-default.abs-left-bottom.type-2 .content {
  gap: 20px;
}
.collection-default.type-xl .content {
  gap: 10px;
}
.collection-default.type-xl .box-title {
  gap: 6px;
}
.collection-default.abs-x-center {
  position: relative;
  border-radius: 40px;
  overflow: hidden;
}
.collection-default.abs-x-center .content {
  gap: 12px;
  position: absolute;
  right: 15px;
  left: 15px;
  z-index: 2;
}
.collection-default.abs-x-center .content .box-title {
  gap: 6px;
  display: grid;
}
.collection-default.abs-x-center.abs-bottom .content {
  bottom: 15px;
}
.collection-default.abs-x-center.abs-top .content {
  top: 15px;
}
.collection-default.abs-x-center.abs-top-2 .content {
  top: 30px;
}
.collection-default.wd-load img {
  min-height: 350px;
}

.collection-position {
  position: relative;
}
.collection-position .img-style {
  display: block;
  width: 100%;
  height: 100%;
}
.collection-position .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.collection-position .content {
  display: grid;
  gap: 8px;
  position: absolute;
  top: 50%;
  left: 15px;
  right: 15px;
  transform: translateY(-50%);
  text-align: center;
}
.collection-position.radius {
  border-radius: 20px;
  overflow: hidden;
}
.collection-position.style-1 {
  border-radius: 12px;
  overflow: hidden;
}
.collection-position.style-1 .content {
  bottom: 36px;
  left: 40px;
  top: unset;
  transform: none;
  text-align: start;
}

.collection-position-2 {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}
.collection-position-2 .img-style {
  display: block;
  width: 100%;
  height: 100%;
}
.collection-position-2 .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.collection-position-2 .content {
  position: absolute;
  left: 15px;
  right: 15px;
  bottom: 15px;
  z-index: 5;
}
.collection-position-2:not(.style-2, .style-3) .cls-btn .icon {
  transform: scale(0);
  transform-origin: right;
  transition: all 0.2s ease;
  color: var(--primary);
}
.collection-position-2:not(.style-2, .style-3) .cls-btn:hover {
  color: var(--primary);
}
.collection-position-2:not(.style-2, .style-3) .cls-btn:hover .icon {
  width: 10px;
  min-width: 10px;
  margin-left: 4px;
  transform: scale(1);
}
.collection-position-2 .cls-btn {
  background-color: var(--white);
  color: var(--main);
  border-radius: 99px;
  padding: 8px 28px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.collection-position-2 .cls-btn .icon {
  width: 0;
  display: inline-block;
  font-size: 16px;
}
.collection-position-2 .cls-btn .text {
  color: inherit;
  z-index: 1;
}
.collection-position-2 .cls-btn:hover {
  color: var(--primary);
}
.collection-position-2.style-3 .cls-btn, .collection-position-2.style-2 .cls-btn {
  justify-content: space-between;
}
.collection-position-2.style-3 .cls-btn .icon, .collection-position-2.style-2 .cls-btn .icon {
  width: 10px;
  min-width: 10px;
  color: inherit;
  position: absolute;
  right: 28px;
  width: 20px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(10px);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: var(--primary);
}
.collection-position-2.style-3 .cls-btn .count-item, .collection-position-2.style-2 .cls-btn .count-item {
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.collection-position-2.style-6 .content, .collection-position-2.style-4 .content {
  text-align: center;
}
.collection-position-2.style-6 .cls-btn, .collection-position-2.style-4 .cls-btn {
  display: inline-flex;
}
.collection-position-2.style-5 .content {
  text-align: center;
  display: grid;
  gap: 4px;
}
.collection-position-2.style-7 .content {
  top: 32px;
  bottom: unset !important;
}
.collection-position-2 .on-sale-wrap {
  position: absolute;
  top: 10px;
  right: 10px;
  left: 10px;
  z-index: 5;
  display: flex;
}
.collection-position-2 .on-sale-wrap .on-sale-item {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  word-break: break-word;
  padding: 0 6px;
  min-width: 50px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  line-height: 20px;
  letter-spacing: 1px;
  text-transform: capitalize;
  position: relative;
  background-color: var(--critical);
  color: var(--white);
  border-radius: 144px;
}
.collection-position-2 .cls-content {
  padding: 15px;
  border-radius: 8px;
  background-color: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  flex-wrap: wrap;
}
.collection-position-2 .cls-content .cls-info {
  display: grid;
  gap: 4px;
}
.collection-position-2 .cls-content .cls-info .link {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.collection-position-2 .cls-content .price {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  line-height: 26px;
}
.collection-position-2 .cls-content .price span {
  color: inherit;
}
.collection-position-2 .cls-content .price .old-price {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: var(--secondary);
  margin-right: 8px;
  text-decoration: line-through;
  display: inline-block;
}
.collection-position-2 .cls-content .cls-btn {
  padding: 12px 28px;
  background-color: var(--main);
  color: var(--white);
}
.collection-position-2 .cls-content .cls-btn:hover {
  background-color: var(--primary);
  color: var(--white);
}
.collection-position-2.style-3 .content:hover .icon, .collection-position-2.style-2 .content:hover .icon {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}
.collection-position-2.style-3 .content:hover .count-item, .collection-position-2.style-2 .content:hover .count-item {
  opacity: 0;
  visibility: hidden;
}
.collection-position-2.style-8 .top {
  position: absolute;
  text-align: center;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
}
.collection-position-2.has-overlay .img-style::after {
  position: absolute;
  content: "";
  height: 152px;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  z-index: 1;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.collection-position-2.has-overlay:hover .img-style::after {
  height: 100%;
}

.collection-position-3 {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}
.collection-position-3 .img-style {
  display: block;
  width: 100%;
  height: 100%;
}
.collection-position-3 .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.collection-position-3 .archive-top {
  display: grid;
  gap: 8px;
  position: absolute;
  z-index: 1;
  left: 15px;
  top: 15px;
}
.collection-position-3 .archive-btn {
  position: absolute;
  z-index: 1;
  left: 15px;
  bottom: 30px;
}

.collection-social {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  height: 400px;
}
.collection-social .img-style {
  display: block;
  width: 100%;
  height: 100%;
}
.collection-social .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.collection-social .cls-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  padding: 12px;
  z-index: 3;
}
.collection-social .cls-content .info {
  flex-grow: 1;
}
.collection-social .cls-content .avatar {
  flex-shrink: 0;
}
.collection-social.style-2 .cls-content {
  background-color: rgba(255, 255, 255, 0.5);
}
.collection-social video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  cursor: pointer;
}
.collection-social:hover video {
  pointer-events: auto;
}
.collection-social .poster {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 2;
  transition: opacity 0.5s ease;
  cursor: pointer;
}
.collection-social .poster.hide {
  opacity: 0;
  pointer-events: none;
}

.banner-cls {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}
.banner-cls .img-style {
  display: block;
  width: 100%;
  height: 100%;
}
.banner-cls .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner-cls .cls-content {
  display: grid;
  gap: 30px;
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
}
.banner-cls .cls-content .box-title-cls {
  display: grid;
  gap: 8px;
}
.banner-cls.style-center {
  bottom: auto;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
}

.collection-circle .img-style {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}
.collection-circle .img-style.radius-48 {
  border-radius: 48px;
}
.collection-circle .collection-content {
  margin-top: 16px;
  display: grid;
  gap: 4px;
}
.collection-circle .collection-content .cls-title {
  color: var(--main);
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.collection-circle .collection-content .cls-title .text {
  color: inherit;
  display: inline-block;
}
.collection-circle .collection-content .cls-title .icon {
  width: 0;
  display: inline-block;
  font-size: 16px;
  color: var(--primary);
  transform: scale(0);
  transform-origin: right;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.collection-circle .collection-content .cls-title:hover {
  color: var(--primary);
}
.collection-circle .collection-content .cls-title:hover .icon {
  width: 10px;
  min-width: 10px;
  margin-left: 4px;
  transform: scale(1);
}
.collection-circle.style-1 .collection-content {
  margin-top: 20px;
}
.collection-circle.style-1 .collection-content .heading {
  margin-bottom: 12px;
}

.flat-collection-circle {
  position: relative;
}
.flat-collection-circle .nav-sw {
  position: absolute;
  z-index: 20;
  top: 36%;
}
.flat-collection-circle .nav-sw.space-1 {
  top: 28%;
}
.flat-collection-circle .nav-sw.space-2 {
  top: 38.5%;
}
.flat-collection-circle .nav-sw-left {
  left: 0;
}
.flat-collection-circle .nav-sw-right {
  right: 0;
}

.list-collection {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.list-collection .cls-item {
  display: flex;
  align-items: center;
  gap: 16px;
}
.list-collection .cls-item .img-cls {
  width: 60px;
  height: 80px;
  flex-shrink: 0;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.list-collection .cls-item .img-cls img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.list-collection .cls-item .title-cls {
  display: inline-flex;
}
.list-collection .cls-item .title-cls .text {
  color: inherit;
  white-space: nowrap;
}
.list-collection .cls-item:hover .img-cls {
  transform: rotateY(180deg);
}

.grid-cls-v1 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}
.grid-cls-v1 .collection-position-2 .cls-btn {
  max-width: 200px;
  margin-left: auto;
  margin-right: auto;
}

.grid-cls-v2 .collection-position-2 .cls-btn {
  max-width: 200px;
  margin-left: auto;
  margin-right: auto;
}

.grid-cls-v3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.grid-cls-4 {
  display: grid;
  gap: 15px;
  grid-template-areas: "item1" "item2" "item3";
  grid-template-columns: 1fr;
}
.grid-cls-4 .item1 {
  grid-area: item1;
}
.grid-cls-4 .item2 {
  grid-area: item2;
}
.grid-cls-4 .item3 {
  grid-area: item3;
}

.banner-cls-discover {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
}
.banner-cls-discover .img-style {
  display: block;
  width: 100%;
  height: 100%;
}
.banner-cls-discover .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner-cls-discover .cls-content {
  display: grid;
  gap: 30px;
  position: absolute;
  top: 50%;
  left: 15px;
  right: 15px;
  transform: translateY(-50%);
  text-align: center;
}
.banner-cls-discover .cls-content .box-title-top {
  display: grid;
  gap: 10px;
}

/*------------ product ---------------- */
.card-product {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.card-product .product-img {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  align-items: stretch;
}
.card-product .card-product-wrapper {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  z-index: 20;
}
.card-product .card-product-wrapper img {
  display: block;
  height: 100%;
  width: 100%;
  object-fit: cover;
  object-position: center;
  transition-duration: 700ms;
}
.card-product .card-product-wrapper .img-hover {
  position: absolute;
  inset: 0;
  opacity: 0;
}
.card-product .card-product-wrapper:hover .product-img .img-product {
  opacity: 0;
}
.card-product .card-product-wrapper:hover .product-img .img-hover {
  display: block;
  z-index: 1;
  opacity: 1;
  -webkit-transform: scale(1.05);
  transform: scale(1.05);
}
.card-product .card-product-wrapper:hover .marquee-wrapper-product {
  opacity: 0;
}
.card-product .card-product-wrapper .on-sale-wrap {
  position: absolute;
  top: 5px;
  right: 5px;
  left: 5px;
  z-index: 5;
  display: flex;
}
.card-product .card-product-wrapper .on-sale-wrap .on-sale-item {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  word-break: break-word;
  padding: 0 6px;
  min-width: 50px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  line-height: 20px;
  letter-spacing: 1px;
  text-transform: capitalize;
  position: relative;
  background-color: var(--critical);
  color: var(--white);
  border-radius: 144px;
}
.card-product .card-product-info {
  padding-top: 10px;
  gap: 4px;
  display: grid;
}
.card-product .card-product-info .title {
  font-size: 16px;
  line-height: 24px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  font-weight: 500;
  text-align: start;
}
.card-product .card-product-info .price {
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  display: flex;
  align-items: center;
}
.card-product .card-product-info .old-price {
  text-decoration: line-through;
  display: inline-block;
  margin-right: 8px;
  font-weight: 400;
  color: var(--secondary-2);
  font-size: 14px;
  line-height: 22px;
}
.card-product .card-product-info .btn-main-product {
  border: 1px solid var(--main);
}
.card-product .list-color-product {
  padding: 4px 0px;
}
.card-product .marquee-product {
  position: absolute;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
  display: none;
  flex-direction: row;
  width: 100%;
  transform: none;
  padding: 3px 0px;
}
.card-product .marquee-product .marquee-child-item p {
  padding-left: 8px;
  padding-right: 8px;
}
.card-product .marquee-product .marquee-child-item .icon {
  font-size: 16px;
}
.card-product .marquee-product .marquee-wrapper {
  flex: 0 0 auto;
  min-width: 100%;
  z-index: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  animation: infiniteScroll 12s linear 0s infinite;
  animation-play-state: running;
  animation-delay: 0s;
  animation-direction: normal;
}
.card-product .variant-wrap {
  position: absolute;
  bottom: 0;
  z-index: 3;
  left: 0px;
  right: 0px;
  pointer-events: none;
  transition: 0.3s ease-out 0s;
}
.card-product .list-btn-main {
  position: absolute;
  bottom: 8px;
  left: 5px;
  right: 5px;
  z-index: 5;
  transition: 0.3s ease-out 0s;
  display: flex;
  align-items: center;
  gap: 4px;
}
.card-product .list-btn-main .box-icon {
  transition: 0.3s ease-out 0s !important;
  transform: none !important;
}
.card-product .list-product-btn {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 5px;
  top: 5px;
  right: 5px;
  z-index: 6;
}
.card-product .countdown-wrap {
  display: none;
}
.card-product .variant-box {
  text-align: center;
  overflow: hidden;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding: 6px;
  max-height: 40px;
  background-color: rgba(255, 255, 255, 0.8);
}
.card-product .variant-box .countdown__timer {
  display: flex;
  gap: 4px;
}
.card-product .variant-box .countdown__item {
  color: var(--critical);
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 1px;
  font-weight: 600;
}
.card-product .variant-box.style-1 {
  margin: 0 12px 12px 12px;
  padding: 8px;
  background-color: rgba(228, 49, 49, 0.1019607843);
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.card-product .variant-box.style-1 .countdown__item {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.card-product .size-list {
  background: linear-gradient(148.05deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.6) 100%);
}
.card-product .size-list .variant-box {
  display: flex;
  align-items: center;
  gap: 10px;
}
.card-product .size-list .size-item {
  font-size: 12px;
  line-height: 20px;
  font-weight: 600;
  letter-spacing: 1px;
}
.card-product .btn-main-product {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px;
  font-weight: 600;
  letter-spacing: 1px;
  border: 1px solid transparent;
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  background-color: var(--white);
  color: var(--main);
  border-radius: 44px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
}
.card-product .btn-main-product:hover {
  background-color: var(--main);
  color: var(--white);
}
.card-product .box-icon {
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 3px;
  background-color: var(--white);
  color: var(--main);
  position: relative;
  border-radius: 50%;
}
.card-product .box-icon .icon {
  font-size: 20px;
}
.card-product .box-icon svg {
  width: 18px;
}
.card-product .box-icon svg path {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.card-product .box-icon:hover {
  background-color: var(--main) !important;
  color: var(--white) !important;
  border-color: var(--main) !important;
}
.card-product .box-icon:hover .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-8px);
  transition-delay: 0.1s;
}
.card-product .box-icon:hover svg path {
  stroke: var(--white);
}
.card-product .tooltip {
  position: absolute;
  z-index: 202;
  opacity: 0;
  visibility: hidden;
  display: none;
  position: absolute;
  right: 100%;
  border-radius: 2px;
  white-space: nowrap;
  background-color: var(--main);
  color: var(--white);
  font-weight: 400;
  font-size: 12px;
  line-height: 12px;
  padding: 6px 8px 8px;
  max-width: 250px;
  width: max-content;
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24), -webkit-transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
}
.card-product .tooltip::before {
  content: "";
  top: 50%;
  left: unset;
  transform: translateY(-50%) rotate(45deg);
  right: -4px;
  position: absolute;
  background: var(--main);
  width: 8px;
  height: 8px;
  z-index: 100;
}
.card-product.list-st-3 .list-product-btn, .card-product.list-st-2 .list-product-btn, .card-product.style-list .list-product-btn, .card-product.style-7 .list-product-btn, .card-product.style-6 .list-product-btn, .card-product.style-2 .list-product-btn {
  flex-direction: row;
  top: unset;
  left: 5px;
  bottom: 5px;
}
.card-product.list-st-3 .list-product-btn .box-icon:hover .tooltip, .card-product.list-st-2 .list-product-btn .box-icon:hover .tooltip, .card-product.style-list .list-product-btn .box-icon:hover .tooltip, .card-product.style-7 .list-product-btn .box-icon:hover .tooltip, .card-product.style-6 .list-product-btn .box-icon:hover .tooltip, .card-product.style-2 .list-product-btn .box-icon:hover .tooltip {
  transform: none;
}
.card-product.list-st-3 .tooltip, .card-product.list-st-2 .tooltip, .card-product.style-list .tooltip, .card-product.style-7 .tooltip, .card-product.style-6 .tooltip, .card-product.style-2 .tooltip {
  top: -100%;
  margin-top: 5px;
  margin-right: 0;
  transform: translateY(8px);
  right: unset;
}
.card-product.list-st-3 .tooltip::before, .card-product.list-st-2 .tooltip::before, .card-product.style-list .tooltip::before, .card-product.style-7 .tooltip::before, .card-product.style-6 .tooltip::before, .card-product.style-2 .tooltip::before {
  top: 85%;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  right: unset;
}
.card-product.style-3 .list-btn-main .box-icon,
.card-product.style-3 .list-btn-main .btn-main-product {
  border-radius: 3px;
}
.card-product.style-3 .list-btn-main .box-icon .tooltip {
  display: none;
}
.card-product.style-7 .list-product-btn {
  gap: 0;
}
.card-product.style-7 .list-product-btn .box-icon {
  border-radius: 0;
}
.card-product.style-7 .list-product-btn .box-icon:not(:last-child) {
  border-right: 1px solid var(--line);
}
.card-product.style-swatch-img .list-color-product {
  gap: 2px;
}
.card-product.style-swatch-img .list-color-product .list-color-item {
  width: 30px;
  height: 30px;
  padding: 3px;
  border: 1px solid transparent;
  border-radius: 6px;
}
.card-product.style-swatch-img .list-color-product .list-color-item img {
  border-radius: 3px;
  position: relative;
  opacity: 1;
  width: 100%;
  height: 100%;
  object-fit: cover;
  visibility: visible;
}
.card-product .box-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}
.card-product .list-star {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}
.card-product .list-star .icon {
  font-size: 16px;
  color: var(--line);
}
.card-product .list-star .icon:not(:last-child) {
  color: var(--yellow);
}
.card-product .progress {
  --bs-progress-height: 8px;
  --bs-progress-bar-bg: var(--critical);
  --bs-progress-bg: var(--line);
  --bs-progress-border-radius: 1000px;
}
.card-product .progress .progress-bar {
  border-radius: var(--bs-progress-border-radius);
}
.card-product .box-progress-stock .stock-status {
  margin-top: 8px;
  gap: 4px;
}
.card-product .box-progress-stock .stock-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.card-product.style-list {
  display: flex;
  gap: 15px;
}
.card-product.style-list .card-product-wrapper {
  max-width: 360px;
  width: 35%;
}
.card-product.style-list .variant-wrap-list {
  width: 100%;
}
.card-product.style-list .card-product-info {
  flex: 1 1 auto;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: self-start;
  max-width: 60%;
}
.card-product.style-list .card-product-info .old-price {
  margin-right: 8px;
}
.card-product.style-list .list-product-btn {
  display: flex;
  gap: 8px;
}
.card-product.style-list .list-product-btn .btn-main-product {
  max-width: 272px;
  width: 100%;
  border: 1px solid var(--line);
}
.card-product.style-list .list-product-btn .box-icon {
  border: 1px solid var(--line);
}
.card-product.style-list .size-box {
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.card-product.style-list .size-box .size-item {
  font-weight: 500;
  font-size: 12px;
  line-height: 20px;
}
.card-product.style-list .size-box .size-item.disable {
  pointer-events: none;
  background-color: var(--surface);
  color: var(--secondary-2);
  border-color: var(--surface);
}
.card-product.style-list .size-box .size-item:hover {
  border-color: var(--main) !important;
  background-color: transparent !important;
  color: var(--main) !important;
}
.card-product.style-list .size-box .size-item.active {
  background-color: var(--main) !important;
  color: var(--white) !important;
  border-color: var(--main) !important;
}
.card-product.style-list .list-color-product {
  margin-bottom: 8px;
}
.card-product.style-list .list-product-btn {
  position: unset;
  justify-content: flex-start;
}
.card-product.list-st-2 {
  display: flex;
  gap: 15px;
}
.card-product.list-st-2 .card-product-info {
  flex: 1 1 auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: self-start;
  gap: 10px;
  max-width: 68%;
}
.card-product.list-st-2 .card-product-wrapper {
  max-width: 210px;
  width: 32%;
}
.card-product.list-st-2 .card-product-wrapper.type-width-2 {
  max-width: 105px;
}
.card-product.list-st-2 .bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.card-product.list-st-2 .box-icon {
  flex-shrink: 0;
  background-color: var(--surface);
}
.card-product.list-st-2 .list-product-btn {
  position: unset;
  justify-content: flex-start;
}
.card-product.list-st-2.has-border {
  padding: 15px;
  border: 1px solid var(--line);
  border-radius: 4px;
}
.card-product.list-st-3 {
  display: flex;
  gap: 15px;
  border: 1px solid var(--line);
  padding: 15px;
  border-radius: 4px;
  overflow: hidden;
}
.card-product.list-st-3 .inner-wrapper-card {
  max-width: 210px;
  width: 41.8%;
  display: flex;
  flex-direction: column;
}
.card-product.list-st-3 .inner-wrapper-card .box-progress-stock {
  margin-top: 10px;
}
.card-product.list-st-3 .inner-wrapper-card .card-product-wrapper {
  flex-grow: 1;
}
.card-product.list-st-3 .card-product-info {
  flex: 1 1 auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: self-start;
  gap: 15px;
  max-width: 58.2%;
}
.card-product.list-st-3 .card-product-info .inner-top {
  display: grid;
  gap: 4px;
  padding-bottom: 8px;
  margin-bottom: 8px;
  border-bottom: 1px solid var(--line);
}
.card-product.list-st-3 .card-product-info .inner-bottom {
  display: grid;
  gap: 8px;
}
.card-product.list-st-3 .card-product-info .list-product-btn {
  display: flex;
  gap: 5px;
}
.card-product.list-st-3 .card-product-info .list-product-btn .box-icon {
  border: 1px solid var(--line);
}
.card-product.list-st-3 .card-product-info .list-product-btn .box-icon .icon {
  font-size: 20px;
}
.card-product.list-st-3 .card-product-info .list-product-btn .btn-main-product {
  max-width: 176px;
  width: 100%;
}
.card-product.list-st-3 .archive-info-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  width: 100%;
}
.card-product.list-st-3 .list-product-btn {
  position: unset;
  justify-content: flex-start;
}
.card-product .countdown-box .countdown__timer {
  display: flex;
  gap: 10px;
}
.card-product .countdown-box .countdown__item {
  background-color: var(--primary);
  color: var(--white);
  padding: 3px 0px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 48px;
  height: 48px;
}
.card-product .countdown-box .countdown__item .countdown__label {
  font-size: 12px;
  line-height: 16px;
}
.card-product .countdown-box .countdown__item .countdown__value {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
}
.card-product:not(.list-st-3, .list-st-2, .style-list) .card-product-wrapper {
  aspect-ratio: 1/1.33;
}

.loadItem.hidden {
  display: none !important;
}
.loadItem.card-product.style-list:not(.hidden) {
  display: flex !important;
}

.list-color-product {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.list-color-product .list-color-item {
  width: 24px;
  height: 24px;
  border: 1px solid transparent;
  background-color: transparent;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.list-color-product .list-color-item .swatch-value {
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  display: inline-block;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.list-color-product .list-color-item img {
  visibility: hidden;
  width: 18px;
  height: 18px;
  position: absolute;
}
.list-color-product .list-color-item.line {
  border: 1px solid var(--line);
}
.list-color-product .list-color-item.active, .list-color-product .list-color-item:hover {
  border-color: var(--main);
}
.list-color-product .list-color-item.active .swatch-value, .list-color-product .list-color-item:hover .swatch-value {
  border-color: var(--white);
}

.stagger-wrap .stagger-item {
  transition: 0.3s ease-in-out;
  transform: scale(0.5) rotate(90deg) skew(15deg);
  opacity: 0;
}
.stagger-wrap .stagger-item.stagger-finished {
  transform: scale(1) rotate(0deg) skew(0deg);
  opacity: 1;
}

.slider-scroll,
.thumbs-slider {
  display: flex;
  gap: 20px;
}

.tf-product-media-thumbs {
  width: 80px;
  flex-shrink: 0;
  max-height: 687px;
}
.tf-product-media-thumbs .swiper-slide {
  height: max-content;
  width: auto;
}
.tf-product-media-thumbs .swiper-slide .item {
  position: relative;
  height: 100%;
  max-height: 107px;
  max-width: 80px;
}
.tf-product-media-thumbs .swiper-slide .item img {
  border-radius: 4px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.tf-product-media-thumbs .swiper-slide .item::after {
  position: absolute;
  content: "";
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  border: 1px solid transparent;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border-radius: 4px;
}
.tf-product-media-thumbs .swiper-slide.swiper-slide-thumb-active .item::after {
  border-color: var(--main);
}

.tf-product-media-main {
  width: calc(100% - 100px);
}
.tf-product-media-main .item {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  max-height: 687px;
}
.tf-product-media-main .item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbs-bottom .thumbs-slider {
  flex-direction: column;
}
.thumbs-bottom .thumbs-slider .tf-product-media-thumbs {
  order: 1;
  width: 100%;
}
.thumbs-bottom .thumbs-slider .tf-product-media-thumbs .swiper-slide {
  width: auto;
}
.thumbs-bottom .thumbs-slider .tf-product-media-main {
  width: 100%;
}
.thumbs-bottom .thumbs-slider .tf-product-media-main .item {
  max-height: 820px;
}

.tf-product-info-list .tf-product-info-heading {
  padding-bottom: 20px;
  border-bottom: 1px solid var(--line);
  margin-bottom: 20px;
}
.tf-product-info-list .tf-product-info-name {
  margin-bottom: 20px;
}
.tf-product-info-list .tf-product-info-name > .text {
  color: var(--secondary-2);
  letter-spacing: 0.1em;
  margin-bottom: 4px;
}
.tf-product-info-list .tf-product-info-name .name {
  margin-bottom: 12px;
}
.tf-product-info-list .tf-product-info-name > .sub {
  display: flex;
  align-items: center;
  gap: 10px 16px;
  flex-wrap: wrap;
}
.tf-product-info-list .tf-product-info-rate {
  display: flex;
  gap: 4px;
  align-items: center;
}
.tf-product-info-list .tf-product-info-rate .list-start {
  display: flex;
}
.tf-product-info-list .tf-product-info-rate .icon {
  font-size: 15px;
}
.tf-product-info-list .tf-product-info-rate .text {
  color: var(--secondary);
}
.tf-product-info-list .tf-product-info-sold {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
}
.tf-product-info-list .tf-product-info-sold .icon {
  font-size: 20px;
  color: var(--primary);
  animation: tf-ani-flash 2s infinite;
}
.tf-product-info-list .tf-product-info-desc {
  display: flex;
  gap: 12px;
  flex-direction: column;
}
.tf-product-info-list .tf-product-info-desc > p {
  color: var(--secondary);
}
.tf-product-info-list .tf-product-info-liveview {
  display: flex;
  gap: 8px;
  align-items: center;
}
.tf-product-info-list .tf-product-info-liveview .icon {
  font-size: 20px;
}
.tf-product-info-list .tf-product-info-choose-option {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.tf-product-info-list .tf-product-info-by-btn {
  display: flex;
  align-items: center;
  gap: 10px;
}
.tf-product-info-list .tf-product-info-by-btn .box-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 2px solid var(--line);
  font-size: 24px;
}
.tf-product-info-list .tf-product-info-by-btn .box-icon:hover {
  background-color: var(--main);
  color: var(--white);
  border-color: var(--main);
}
.tf-product-info-list .tf-product-info-help {
  padding-bottom: 19px;
  border-bottom: 1px solid var(--line);
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-extra-link {
  display: flex;
  gap: 15px 34px;
  align-items: center;
  flex-wrap: wrap;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-extra-link .tf-product-extra-icon {
  display: flex;
  gap: 4px;
  align-items: center;
  position: relative;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-extra-link .tf-product-extra-icon .icon {
  font-size: 20px;
  line-height: 20px;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-extra-link .tf-product-extra-icon:not(:last-child)::after {
  position: absolute;
  content: "";
  width: 1px;
  height: 20px;
  right: -18px;
  top: 1px;
  background-color: var(--line);
}
.tf-product-info-list .tf-product-info-help .tf-product-info-available,
.tf-product-info-list .tf-product-info-help .tf-product-info-return,
.tf-product-info-list .tf-product-info-help .tf-product-info-time {
  display: flex;
  gap: 4px;
  align-items: center;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-available .icon,
.tf-product-info-list .tf-product-info-help .tf-product-info-return .icon,
.tf-product-info-list .tf-product-info-help .tf-product-info-time .icon {
  font-size: 20px;
  line-height: 20px;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-available p,
.tf-product-info-list .tf-product-info-help .tf-product-info-return p,
.tf-product-info-list .tf-product-info-help .tf-product-info-time p {
  color: var(--secondary);
}
.tf-product-info-list .tf-product-info-help .tf-product-info-available p span,
.tf-product-info-list .tf-product-info-help .tf-product-info-return p span,
.tf-product-info-list .tf-product-info-help .tf-product-info-time p span {
  color: var(--main);
}
.tf-product-info-list .tf-product-info-help .tf-product-info-view {
  display: flex;
  gap: 4px;
  align-items: center;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-view .icon {
  font-size: 20px;
  line-height: 20px;
}
.tf-product-info-list .tf-product-info-help .tf-product-info-view span {
  text-decoration: underline;
  font-size: 14px;
  line-height: 22px;
}
.tf-product-info-list ul.tf-product-info-sku {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-bottom: 19px;
  border-bottom: 1px solid var(--line);
}
.tf-product-info-list ul.tf-product-info-sku li {
  display: flex;
  align-items: center;
  gap: 4px;
}
.tf-product-info-list ul.tf-product-info-sku li .text-1 {
  color: var(--secondary);
}
.tf-product-info-list .tf-product-info-guranteed {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}
.tf-product-info-list .tf-product-info-guranteed .tf-payment {
  gap: 12px;
}
.tf-product-info-list .tf-product-info-guranteed a img {
  width: 50px;
}
.tf-product-info-list .wg-quantity input {
  pointer-events: none;
}

.tf-product-info-price {
  display: flex;
  align-items: center;
}
.tf-product-info-price .price-on-sale {
  margin-right: 8px;
}
.tf-product-info-price .compare-at-price {
  font-size: 16px;
  font-weight: 400;
  line-height: 19.52px;
  color: var(--secondary-2);
  text-decoration: line-through;
  margin-right: 16px;
}
.tf-product-info-price .badges-on-sale {
  padding: 0 8px;
  background-color: var(--critical);
  border-radius: 999px;
  color: var(--white);
  letter-spacing: 0.1em;
}
.tf-product-info-price.type-small .price-on-sale {
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  margin-right: 4px;
}
.tf-product-info-price.type-small .compare-at-price {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-right: 4px;
}
.tf-product-info-price.type-small .badges-on-sale {
  font-size: 10px;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.1em;
}
.tf-product-info-price.type-1 .price-on-sale {
  margin-right: 25px;
}
.tf-product-info-price.type-1 .compare-at-price {
  margin-right: 12px;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  position: relative;
}
.tf-product-info-price.type-1 .compare-at-price::before {
  position: absolute;
  content: "";
  width: 1px;
  height: 16px;
  left: -13px;
  background-color: var(--line);
  top: 50%;
  transform: translateY(-50%);
}
.tf-product-info-price.type-1 .badges-on-sale {
  padding: 2px 12px;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
}

.wg-quantity {
  width: 180px;
  height: 48px;
  display: flex;
  justify-content: space-between;
  background-color: var(--white);
  border: 2px solid var(--line);
  border-radius: 999px;
  overflow: hidden;
}
.wg-quantity input {
  width: 88px;
  height: 44px;
  padding: 0;
  background-color: transparent;
  border: 0;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: var(--main);
}
.wg-quantity .btn-quantity {
  width: 44px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 38px;
  color: var(--main);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.wg-quantity .btn-quantity:hover {
  color: var(--primary);
}
.wg-quantity.style-1 {
  width: 140px;
  height: 44px;
  border-radius: 12px;
  border: 1px solid var(--line);
  background-color: var(--surface);
}
.wg-quantity.style-1 input {
  width: 56px;
}

.variant-picker-item .variant-picker-label span {
  margin-left: 8px;
}
.variant-picker-item .size-guide {
  text-decoration: underline;
}
.variant-picker-item .variant-picker-values {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}
.variant-picker-item .variant-picker-values.gap12 {
  gap: 12px;
}
.variant-picker-item .variant-picker-values.type-click input:checked + label {
  border: 1px solid var(--main);
  padding: 3px;
}
.variant-picker-item .variant-picker-values.type-click input:checked + label.style-text-1 {
  background-color: var(--main);
  border: 2px solid var(--main);
}
.variant-picker-item .variant-picker-values.type-click input:checked + label.style-text-1 span {
  color: var(--white);
}
.variant-picker-item .variant-picker-values input {
  position: absolute !important;
  overflow: hidden;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  clip: rect(0 0 0 0);
  word-wrap: normal !important;
}
.variant-picker-item .variant-picker-values input:checked + label.style-text {
  background-color: var(--main);
  border: 1px solid var(--main);
}
.variant-picker-item .variant-picker-values input:checked + label.style-text span {
  color: var(--white);
}
.variant-picker-item .variant-picker-values label {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0px;
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: 400;
  line-height: 22.4px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.variant-picker-item .variant-picker-values label .btn-checkbox {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
  border: 3px solid transparent;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.variant-picker-item .variant-picker-values label.style-text {
  width: 48px;
  height: 48px;
  border: 2px solid var(--line);
  border-radius: 50%;
  padding: 7px 15px;
}
.variant-picker-item .variant-picker-values label.style-text:hover {
  border-color: var(--main);
}
.variant-picker-item .variant-picker-values label.style-text-1 {
  gap: 8px;
  width: unset;
  height: 48px;
  border: 2px solid var(--line);
  border-radius: 999px;
  padding: 10px 18px !important;
}
.variant-picker-item .variant-picker-values label.style-text-1:hover {
  border-color: var(--main);
}
.variant-picker-item .variant-picker-values label.style-text-1 .circle-color {
  display: flex;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}
.variant-picker-item .variant-picker-values label.type-disable {
  pointer-events: none;
  background-color: var(--surface);
  color: var(--secondary-2);
  border-color: var(--surface);
  cursor: no-drop;
}
.variant-picker-item .variant-picker-values label.type-sold-out {
  overflow: hidden;
}
.variant-picker-item .variant-picker-values label.type-sold-out::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 32px;
  height: 1.2px;
  border-bottom: 1.2px dashed var(--secondary-2);
}
.variant-picker-item .variant-picker-values label.type-sold-out::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  width: 32px;
  height: 1.2px;
  border-bottom: 1.2px dashed var(--secondary-2);
}
.variant-picker-item .variant-picker-values label.style-image {
  display: flex;
  flex-direction: column;
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 4px;
  width: 72px;
  height: 92px;
}
.variant-picker-item .variant-picker-values label.style-image img {
  border-radius: 4px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.variant-picker-item .variant-picker-values label.style-image-rounded {
  display: flex;
  flex-direction: column;
  border: 2px solid var(--line);
  border-radius: 50%;
  padding: 2px;
  width: 48px;
  height: 48px;
}
.variant-picker-item .variant-picker-values label.style-image-rounded img {
  border-radius: 50%;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.variant-picker-item .other-variant-btn.line,
.variant-picker-item .color-btn.line {
  border-color: var(--line);
}
.variant-picker-item .other-variant-btn.active,
.variant-picker-item .color-btn.active {
  border-color: var(--main);
}
.variant-picker-item .other-variant-btn.active .btn-checkbox,
.variant-picker-item .color-btn.active .btn-checkbox {
  border-color: var(--white);
}
.variant-picker-item .other-variant-btn.style-text-1.active,
.variant-picker-item .color-btn.style-text-1.active {
  background-color: var(--main);
  border-color: var(--main);
}
.variant-picker-item .other-variant-btn.style-text-1.active span,
.variant-picker-item .color-btn.style-text-1.active span {
  color: var(--white);
}
.variant-picker-item .other-variant-btn.style-image.active,
.variant-picker-item .color-btn.style-image.active {
  border-color: var(--main);
}
.variant-picker-item .other-variant-btn.style-image-rounded.active,
.variant-picker-item .color-btn.style-image-rounded.active {
  border-color: var(--main);
}
.variant-picker-item .variant-other-size .btn-size {
  padding: 12px 25px;
  color: var(--secondary);
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  border-radius: 4px;
  border: 2px solid var(--line);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: var(--white);
}
.variant-picker-item .variant-other-size .btn-size:hover, .variant-picker-item .variant-other-size .btn-size.active {
  border-color: var(--main);
  color: var(--main);
}

.value-currentVariant,
.select-currentColor,
.value-currentColor {
  text-transform: capitalize;
}

.product-description-list .product-description-list-item:not(:last-child) {
  margin-bottom: 40px;
}
.product-description-list .product-description-list-item .product-description-list-title {
  padding-bottom: 11px;
  border-bottom: 1px solid var(--main);
  margin-bottom: 20px;
}
.product-description-list .product-description-list-item .product-description-list-content {
  border-radius: 8px;
  padding: 40px;
  border: 1px solid var(--line);
}

.tf-main-product.full-width {
  display: flex;
  padding: 0 60px;
  padding: 0 43px 0 60px;
  gap: 0 80px;
}
.tf-main-product.full-width > div {
  width: calc(50% - 40px);
}
.tf-main-product.full-width .thumbs-slider,
.tf-main-product.full-width .tf-product-info-list {
  max-width: unset !important;
}
.tf-main-product.full-width .tf-product-media-thumbs,
.tf-main-product.full-width .tf-product-media-main .item {
  max-height: 1013px;
}

.product-fixed-price .grid-image-top {
  display: flex;
  gap: 32px 30px;
  margin-bottom: 60px;
}
.product-fixed-price .grid-image-top img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.product-fixed-price .grid-image-top .item-3 {
  margin-bottom: 32px;
}
.product-fixed-price .left-desc {
  margin-left: unset;
  padding: 0 !important;
  max-width: 710px;
}
.product-fixed-price .right-details {
  border-radius: 8px;
  box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.1490196078);
  padding: 32px;
  max-width: unset;
}

.product-fixed-scroll .accordion-product-wrap {
  padding-top: 40px;
}

.frequently-bought-together-2 {
  margin-top: 40px;
}

.tf-bundle-product-item {
  display: flex;
  gap: 16px;
  align-items: center;
}
.tf-bundle-product-item .tf-product-bundle-image {
  flex-shrink: 0;
  width: 102px;
  height: 133px;
  border-radius: 8px;
  overflow: hidden;
}
.tf-bundle-product-item .tf-product-bundle-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.tf-bundle-product-item .tf-product-bundle-infos {
  display: flex;
  gap: 12px;
  flex-direction: column;
}
.tf-bundle-product-item .tf-product-bundle-infos .text-title {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.tf-bundle-product-item .tf-check {
  flex-shrink: 0;
  width: 21px;
  height: 21px;
  margin: 3.5px;
  border-width: 2px;
  border-radius: 2px;
}
.tf-bundle-product-item .tf-check::before {
  font-size: 12px;
}

.tf-product-upsell {
  padding-bottom: 20px;
  border-bottom: 1px solid var(--line);
}
.tf-product-upsell .tf-product-upsell-slod .tf-product-process-wrap {
  width: 100%;
  max-width: 380px;
}
.tf-product-upsell .tf-product-upsell-slod .tf-product-process-wrap .progress {
  height: 8px;
  background-color: var(--line);
  margin-bottom: 8px;
}
.tf-product-upsell .tf-product-upsell-slod .tf-product-process-wrap .progress .progress-bar-striped {
  background-size: 16px 16px;
  background-color: var(--critical);
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.tf-product-pre-order {
  padding: 4px 16px;
  background-color: var(--main);
  border-radius: 99px;
  width: max-content;
  color: var(--white);
}

@-webkit-keyframes progress-bar-stripes {
  0% {
    background-position-x: -1rem;
  }
}
@keyframes progress-bar-stripes {
  0% {
    background-position-x: -1rem;
  }
}
.tf-product-customer-note .tf-product-image-upload {
  position: relative;
}
.tf-product-customer-note .tf-product-image-upload label {
  width: 100%;
  height: 48px;
  padding: 9px 14px;
  border-radius: 8px;
  border: 2px solid var(--line);
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  cursor: pointer;
}
.tf-product-customer-note .tf-product-image-upload input {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 94px;
  height: 32px;
}
.tf-product-customer-note .tf-product-image-upload input::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 94px;
  height: 32px;
  background-color: var(--white);
}
.tf-product-customer-note .tf-product-image-upload input::after {
  position: absolute;
  content: "UP LOAD";
  left: 0;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: 0.1em;
  padding: 6px 16px;
  border-radius: 999px;
  background-color: var(--main);
  color: var(--white);
  cursor: pointer;
}

.tf-product-out-of-stock {
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--line);
}
.tf-product-out-of-stock .form-out-of-stock button {
  border: 1px solid var(--main);
}

.tf-product-deals {
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--line);
  background-color: var(--surface);
}

.tf-product-deals-list .tf-product-deals-item:not(:last-child) {
  margin-bottom: 12px;
}

.tf-product-deals-grid {
  display: flex;
  gap: 12px;
  overflow-x: auto;
}
.tf-product-deals-grid::-webkit-scrollbar {
  height: 8px;
}
.tf-product-deals-grid::-webkit-scrollbar-thumb {
  background: var(--secondary-2);
}
.tf-product-deals-grid::-webkit-scrollbar-track {
  background: var(--line);
}

.tf-product-deals-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  flex-wrap: wrap;
  padding: 10px 19px;
  border: 2px solid var(--line);
  border-radius: 8px;
  background-color: var(--white);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-product-deals-item .tf-check-rounded::before {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-product-deals-item.select-option, .tf-product-deals-item:hover {
  border-color: var(--main);
}
.tf-product-deals-item.select-option .tf-check-rounded::before, .tf-product-deals-item:hover .tf-check-rounded::before {
  opacity: 1;
}
.tf-product-deals-item.style-column {
  gap: 12px;
  justify-content: center;
  min-width: 181px;
}
.tf-product-deals-item.style-column .image {
  max-width: 140px;
  max-height: 140px;
  border-radius: 8px;
  overflow: hidden;
}
.tf-product-deals-item.style-column .tf-product-info-price {
  justify-content: center;
  flex-wrap: wrap;
  gap: 4px 8px;
}
.tf-product-deals-item.style-column .tf-product-info-price > div {
  margin-right: 0;
}

.tf-product-with-discount .tf-product-discount-list {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
.tf-product-with-discount .tf-product-discount-list .tf-product-discount-item {
  position: relative;
}
.tf-product-with-discount .tf-product-discount-list .tf-product-discount-item .tf-number-discount {
  position: absolute;
  top: 5px;
  right: 59px;
  color: var(--white);
}
.tf-product-with-discount .tf-product-discount-list .tf-product-discount-item .tf-btn-discount {
  position: absolute;
  top: 6px;
  right: 6px;
  background-color: var(--white);
  border-radius: 999px;
  padding: 3px 6px;
  font-size: 10px;
  font-weight: 600;
  line-height: 14px;
  letter-spacing: 0.1em;
  cursor: pointer;
}

.tf-product-subscribe-save .tf-product-subscribe {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.tf-product-subscribe-save .tf-product-subscribe-item {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.tf-product-subscribe-save .tf-product-subscribe-item > label {
  cursor: pointer;
  padding-left: 32px;
}
.tf-product-subscribe-save .tf-product-subscribe-item > input {
  cursor: pointer;
  position: absolute;
  top: 7px;
  left: 3px;
  accent-color: var(--main);
}
.tf-product-subscribe-save input:checked ~ .tf-product-box-save {
  display: block;
}
.tf-product-subscribe-save .tf-product-box-save {
  display: none;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid var(--line);
}

.tf-sticky-btn-atc {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 70;
  box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);
  background-color: var(--white);
  transform: translateY(100%);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-sticky-btn-atc .form-sticky-atc {
  padding: 10px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tf-sticky-btn-atc .form-sticky-atc .tf-dropdown-sort {
  padding: 8px 10px;
}
.tf-sticky-btn-atc .tf-sticky-atc-product {
  display: flex;
  gap: 16px;
}
.tf-sticky-btn-atc .tf-sticky-atc-product .image {
  width: 56px;
  height: 74.67px;
  border-radius: 8px;
  overflow: hidden;
}
.tf-sticky-btn-atc .tf-sticky-atc-product .content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.tf-sticky-btn-atc .tf-sticky-atc-infos {
  display: flex;
  gap: 32px;
  align-items: center;
}
.tf-sticky-btn-atc .tf-sticky-atc-infos .tf-sticky-atc-btns {
  width: 274px;
}
.tf-sticky-btn-atc.show {
  transform: translateY(0);
}

.tf-add-cart-success {
  position: fixed;
  top: 200px;
  right: -413px;
  width: 353px;
  height: 264px;
  padding: 20px 24px;
  border-radius: 24px;
  background-color: var(--white);
  box-shadow: 0px 10px 25px 0px rgba(43, 52, 74, 0.12);
  z-index: 200;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  transition-delay: 0s !important;
}
.tf-add-cart-success .tf-add-cart-heading {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--line);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tf-add-cart-success .tf-add-cart-heading .icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border-radius: 50%;
  background-color: var(--surface);
  cursor: pointer;
}
.tf-add-cart-success .tf-add-cart-product {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}
.tf-add-cart-success .tf-add-cart-product .image {
  width: 56px;
  height: 74.67px;
  border-radius: 8px;
  overflow: hidden;
}
.tf-add-cart-success .tf-add-cart-product .content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.tf-add-cart-success.active {
  right: 60px;
  transition-delay: 0.5s !important;
}

.tf-has-purchased {
  position: fixed;
  top: 30%;
  right: -331px;
  width: 331px;
  height: 116px;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--white);
  box-shadow: 0px 10px 25px 0px rgba(43, 52, 74, 0.12);
  z-index: 199;
  -webkit-transition: all 0.6s ease;
  -moz-transition: all 0.6s ease;
  -ms-transition: all 0.6s ease;
  -o-transition: all 0.6s ease;
  transition: all 0.6s ease;
}
.tf-has-purchased .icon-close {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border-radius: 50%;
  background-color: var(--line);
  cursor: pointer;
}
.tf-has-purchased .tf-has-purchased-product {
  display: flex;
  gap: 16px;
}
.tf-has-purchased .tf-has-purchased-product .image {
  width: 63px;
  height: 84px;
  border-radius: 4px;
  overflow: hidden;
}
.tf-has-purchased.active {
  right: 60px;
}

.tf-product-stacked .item:first-child {
  grid-column: 1/3;
}

.flat-single-home .tf-product-info-list {
  max-width: 100%;
}
.flat-single-home .tf-product-info-list .tf-product-info-rate {
  margin-bottom: 12px;
}
.flat-single-home .tf-product-info-list .tf-product-info-price .price-on-sale {
  margin-right: 24px;
}
.flat-single-home .tf-product-info-list .tf-product-info-price .old-price {
  margin-right: 12px;
  position: relative;
}
.flat-single-home .tf-product-info-list .tf-product-info-price .old-price::before {
  position: absolute;
  content: "";
  width: 1px;
  height: 16px;
  left: -13px;
  background-color: var(--line);
  top: 50%;
  transform: translateY(-50%);
}
.flat-single-home .tf-product-info-list .tf-product-info-price .old-price-sold {
  color: var(--secondary-2);
  text-decoration: line-through;
}
.flat-single-home .tf-product-info-list .tf-product-info-heading {
  padding-bottom: 0;
  border: none;
  margin-bottom: 24px;
}
.flat-single-home .tf-product-info-list .wg-quantity,
.flat-single-home .tf-product-info-list .btn-style-2,
.flat-single-home .tf-product-info-list .btn-style-3 {
  border-radius: 4px;
}
.flat-single-home .tf-product-info-list .box-icon {
  border-radius: 8px;
}
.flat-single-home .variant-picker-item .variant-picker-values {
  gap: 8px;
}
.flat-single-home .variant-picker-item .variant-picker-values .type-disable {
  background-color: var(--line);
  border-color: var(--line);
}
.flat-single-home .variant-picker-item .variant-color .color-btn {
  width: 24px;
  height: 24px;
}
.flat-single-home .thumbs-slider {
  max-width: 100%;
  gap: 10px;
}
.flat-single-home .tf-product-media-thumbs .item {
  max-height: 133px;
  max-width: 100px;
}

.tf-buyx-gety-wrap {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.tf-buyx-gety-wrap .tf-buyx-gety-deviced {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.tf-buyx-gety-wrap .tf-buyx-gety-deviced::after {
  content: "";
  width: 100%;
  position: absolute;
  height: 2px;
  background: var(--main);
  top: 50%;
  transform: translateY(-50%);
}
.tf-buyx-gety-wrap .tf-buyx-gety-deviced span {
  display: flex;
  align-items: center;
  z-index: 9;
  justify-content: center;
  background-color: var(--surface);
  border-radius: 50%;
}

.tf-buyx-gety-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
  border: 1px solid var(--line);
  border-radius: 12px;
  padding: 19px;
  background-color: var(--white);
}
.tf-buyx-gety-item .title {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.tf-buyx-gety-item .image {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}
.tf-buyx-gety-item .tags {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 0 12px;
  background-color: #f03e3e;
  border-radius: 99px;
}

/*------------ blog ---------------- */
.meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px 33px;
}
.meta .meta-item {
  position: relative;
  display: flex;
  align-items: center;
}
.meta .meta-item .icon {
  font-size: 24px;
}
.meta .meta-item:not(:last-child)::after {
  position: absolute;
  content: "";
  width: 1px;
  height: 16px;
  background-color: var(--line);
  right: -17px;
  top: 50%;
  transform: translateY(-50%);
}

.hover-image .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 1s;
}
.hover-image:hover .image img {
  transform: scale(1.1);
}

.wg-blog .image {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 32px;
}
.wg-blog .content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.wg-blog .content .body-text-1 {
  color: var(--secondary);
}
.wg-blog .content .title {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.wg-blog .content .title a {
  display: inline;
  background-repeat: no-repeat;
  background-position-y: 0px;
  background-image: linear-gradient(transparent calc(100% - 1px), currentColor 1px);
  transition: 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.wg-blog .content .btn-readmore {
  color: #696c70;
  text-decoration: underline;
  margin-top: 8px;
}
.wg-blog:hover .title a {
  background-size: 100% 100%;
  transition-delay: 0.2s;
  font-weight: 600;
}
.wg-blog.style-1 .image {
  margin-bottom: 24px;
}
.wg-blog.style-1 .content {
  gap: 12px;
}
.wg-blog.style-1 .meta-item .icon {
  font-size: 16px;
}
.wg-blog.style-1 .title {
  margin-bottom: 8px;
}
.wg-blog.style-1 .body-text {
  color: var(--secondary);
}
.wg-blog.style-row {
  display: flex;
  align-items: center;
}
.wg-blog.style-row .content {
  padding: 20px 0px 20px 40px;
  width: 53%;
}
.wg-blog.style-row .image {
  margin: 0;
  width: 47%;
}
.wg-blog.style-row .meta-item .icon {
  font-size: 16px;
}
.wg-blog.style-row .bot-button {
  text-decoration: underline;
  text-decoration-thickness: 1px;
}
.wg-blog.style-abs {
  position: relative;
}
.wg-blog.style-abs .image {
  margin-bottom: 0px;
  border-radius: 20px;
  display: block;
}
.wg-blog.style-abs .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.wg-blog.style-abs .content {
  position: absolute;
  left: 28px;
  right: 28px;
  bottom: 24px;
  gap: 8px;
}

.wg-pagination {
  display: flex;
  gap: 8px;
}
.wg-pagination .pagination-item {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--line);
  border-radius: 5px;
  overflow: hidden;
}
.wg-pagination li:hover .pagination-item,
.wg-pagination li.active .pagination-item {
  background-color: var(--main);
  border-color: var(--main);
  color: var(--white);
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.sidebar .sidebar-heading {
  font-weight: 500;
  margin-bottom: 20px;
}
.sidebar .sidebar-categories ul {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.sidebar .sidebar-writer .writer-avatar {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}
.sidebar .sidebar-writer .writer-avatar .image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
}
.sidebar .sidebar-writer .writer-avatar .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.sidebar .sidebar-writer .writer-avatar .name {
  margin-bottom: 12px;
}
.sidebar .sidebar-writer .writer-avatar .name h6 {
  margin-bottom: 4px;
}
.sidebar .sidebar-writer .writer-avatar .name p {
  color: var(--secondary);
}
.sidebar .sidebar-writer .writer-avatar .button-follow {
  padding: 6px 14px;
  border-radius: 999px;
  background-color: var(--main);
  color: var(--white);
  letter-spacing: 0.1em;
}
.sidebar .sidebar-writer .writer-content p {
  color: var(--secondary);
  margin-bottom: 16px;
}

.relatest-post-item .image {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
}
.relatest-post-item .content {
  display: grid;
  gap: 12px;
  flex-grow: 1;
}
.relatest-post-item .meta .icon {
  font-size: 16px;
  line-height: 1;
}
.relatest-post-item .title a {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.relatest-post-item:not(:last-child) {
  padding-bottom: 19px;
  border-bottom: 1px solid var(--line);
  margin-bottom: 20px;
}
.relatest-post-item.style-row {
  display: flex;
  gap: 20px;
  align-items: center;
}
.relatest-post-item.style-row .content {
  gap: 8px;
}
.relatest-post-item.style-row .image {
  margin-bottom: 0;
  width: 112px;
  flex-shrink: 0;
}
.relatest-post-item.style-row .meta {
  gap: 10px 17px;
}
.relatest-post-item.style-row .meta p {
  color: var(--main);
}
.relatest-post-item.style-row .meta .meta-item:not(:last-child)::after {
  right: -9px;
}

ul.list-tags {
  display: flex;
  align-items: center;
  gap: 11px;
  flex-wrap: wrap;
}
ul.list-tags a {
  display: flex;
  padding: 4px 15px;
  border-radius: 5px;
  border: 1px solid var(--line);
}
ul.list-tags.has-bg a {
  border-radius: 40px;
  padding: 4px 16px 4px 16px;
  background-color: var(--surface);
  border: 0;
}

.new-item {
  display: flex;
  align-items: center;
  gap: 24px;
}
.new-item .img-style {
  max-width: 303px;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}
.new-item .content {
  display: grid;
  gap: 12px;
}
.new-item .content .title-box {
  display: grid;
  gap: 8px;
}

/*------------ testimonial ---------------- */
.testimonial-item {
  display: flex;
  border: 1px solid var(--line);
  background-color: var(--white);
  border-radius: 8px;
  overflow: hidden;
}
.testimonial-item .img-style {
  max-width: 234px;
  width: 100%;
  position: relative;
  flex-shrink: 0;
}
.testimonial-item .img-style img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.testimonial-item .img-style .box-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  background-color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--main);
  font-size: 18px;
  border-radius: 50%;
  z-index: 5;
}
.testimonial-item .img-style .box-icon:hover {
  background-color: var(--main);
  color: var(--white);
}
.testimonial-item .img-style::before {
  position: absolute;
  z-index: 2;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  top: 0;
  left: 0;
  transition: 0.4s ease 0.1s;
  opacity: 0;
  visibility: hidden;
}
.testimonial-item .content {
  padding: 26px;
}
.testimonial-item .content-top {
  display: grid;
  gap: 8px;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--line);
}
.testimonial-item .box-author {
  display: flex;
  align-items: center;
  gap: 6px;
}
.testimonial-item .box-avt {
  display: flex;
  gap: 16px;
  align-items: center;
}
.testimonial-item .box-avt img {
  transform: none;
}
.testimonial-item .box-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--main);
  color: var(--white);
}
.testimonial-item .box-icon .icon {
  font-size: 16px;
}
.testimonial-item .box-rate-author {
  margin-top: 8px;
  display: grid;
  gap: 4px;
}
.testimonial-item.style-2 {
  padding: 24px;
  display: block;
}
.testimonial-item.style-2 .content-top {
  padding-bottom: 16px;
}
.testimonial-item.style-3 {
  border: none;
  border-radius: 12px;
}
.testimonial-item.style-4 {
  border-radius: 12px;
  padding: 32px;
  display: block;
  border: 2px solid var(--line);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.testimonial-item.style-4 .content-top {
  padding-bottom: 0px;
  border: 0;
}
.testimonial-item.style-4:hover {
  border-color: var(--main);
}
.testimonial-item.style-row {
  flex-direction: column;
}
.testimonial-item.style-row .img-style {
  max-width: 100%;
  height: 273px;
}
.testimonial-item.no-border {
  border: 0;
}
.testimonial-item:hover .img-style::before {
  opacity: 1;
  visibility: visible;
}

.testimonial-item-v2 {
  display: grid;
  gap: 16px;
}
.testimonial-item-v2 .quote-box {
  display: grid;
  gap: 16px;
}
.testimonial-item-v2 .quote-box .icon {
  font-size: 40px;
}
.testimonial-item-v2 .tes-box {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: start;
  gap: 8px;
  padding: 12px;
  border-radius: 12px;
  border: 1px solid var(--line);
}
.testimonial-item-v2 .rate-box {
  margin-left: auto;
  margin-right: auto;
  display: grid;
  gap: 8px;
}
.testimonial-item-v2 .rate-box .list-star-default {
  justify-content: center;
  gap: 4px;
}
.testimonial-item-v2 .rate-box .list-star-default .icon {
  font-size: 22px;
  color: var(--primary);
}

.tf-sw-testimonial .box-navigation {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.testimonial-item-2 {
  display: grid;
  gap: 20px;
}
.testimonial-item-2 .content-top {
  display: grid;
  gap: 12px;
}
.testimonial-item-2 .box-author {
  display: flex;
  align-items: center;
  gap: 20px;
}
.testimonial-item-2 .box-author .info {
  display: grid;
  gap: 4px;
}
.testimonial-item-2 .box-author .avt {
  max-width: 68px;
  border-radius: 50%;
  overflow: hidden;
}

/*------------ lookbook ---------------- */
.tf-pin-btn {
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: relative;
}
.tf-pin-btn span {
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: transparent;
  border: 8px solid var(--white);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-pin-btn span::after, .tf-pin-btn span::before {
  position: absolute;
  content: "";
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: solid 1px var(--white);
  border-radius: 50%;
}
.tf-pin-btn span::before {
  animation: ripple-line 2s linear infinite;
}
.tf-pin-btn span::after {
  animation: ripple-line 2s 1s linear infinite;
}
.tf-pin-btn.style-lg span {
  width: 28px;
  height: 28px;
}
.tf-pin-btn.style-hover:hover span {
  background-color: var(--primary);
}
.tf-pin-btn.style-dark {
  width: 28px;
  height: 28px;
  background-color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.tf-pin-btn.style-dark span {
  background-color: var(--main);
  width: 12px;
  height: 12px;
  border: 0;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-pin-btn.style-dark:hover {
  background-color: rgba(255, 255, 255, 0.7);
}
.tf-pin-btn.style-dark:hover span {
  background-color: var(--primary);
}

.collection-position-3 {
  position: relative;
}

.cls-lookbook {
  overflow: unset;
  position: relative;
}
.cls-lookbook .img-style {
  border-radius: 20px;
  display: block;
}
.cls-lookbook .lookbook-item {
  position: absolute;
  left: 30%;
  bottom: 32%;
}
.cls-lookbook .lookbook-item.position1 {
  left: 40%;
  bottom: 12%;
}
.cls-lookbook .lookbook-item.position2 {
  left: 32%;
  bottom: 18%;
}
.cls-lookbook .lookbook-item.position3 {
  left: 58%;
  bottom: 10%;
}
.cls-lookbook .lookbook-item.position4 {
  left: 36%;
  bottom: 45%;
}
.cls-lookbook .lookbook-item.position5 {
  left: 70%;
  bottom: 16%;
}

.tf-sw-lookbook .cls-lookbook {
  width: 100%;
  height: 100%;
}

.lookbook-item .dropdown-menu {
  border: none;
  border-radius: 0;
  background: transparent;
  padding: 0;
}

.loobook-product {
  box-shadow: var(--shadow1);
  padding: 12px;
  background-color: var(--white);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}
.loobook-product .content {
  text-align: start;
}
.loobook-product .content .btn-lookbook {
  margin-top: 5px;
}
.loobook-product .img-style {
  border-radius: 0;
  flex-shrink: 0;
}
.loobook-product .btn-lookbook {
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
}
.loobook-product .btn-lookbook::after {
  height: 1px;
}
.loobook-product::before {
  content: "";
  position: absolute;
  border-width: 8px;
  border-style: solid;
}
.loobook-product.style-row {
  flex-direction: row;
}
.loobook-product.style-row .img-style {
  max-width: 60px;
  max-height: 100px;
}

.dropup .loobook-product {
  margin-bottom: 20px;
}
.dropup .loobook-product::before {
  border-color: var(--white) transparent transparent transparent;
  bottom: -4%;
  left: 50%;
  transform: translateX(-50%);
}

.lookbook-item .dropend .dropdown-menu {
  --bs-dropdown-min-width: 12rem;
  margin-left: 15px !important;
}
.lookbook-item .dropend .loobook-product {
  margin: -50px 0px;
}
.lookbook-item .dropend .loobook-product::before {
  border-color: transparent #fff transparent transparent;
  top: 50%;
  left: -15px;
  transform: translateY(-50%);
}

.sw-lookbook-wrap {
  padding-top: 70px;
  margin-top: -70px;
}

.banner-lookbook {
  position: relative;
}
.banner-lookbook img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner-lookbook .lookbook-item {
  position: absolute;
  left: 52%;
  bottom: 32%;
}
.banner-lookbook .lookbook-item.position1 {
  left: 49%;
  bottom: 10%;
}
.banner-lookbook .lookbook-item.position2 {
  left: 12%;
  bottom: 35%;
}
.banner-lookbook .lookbook-item.position3 {
  left: 49%;
  bottom: 52%;
}
.banner-lookbook .lookbook-item.position4 {
  left: 70%;
  bottom: 35%;
}
.banner-lookbook .lookbook-item.position5 {
  left: 37.7%;
  bottom: 65.5%;
}

.dropdown-custom .dropdown-menu {
  --bs-dropdown-min-width: 13rem;
}

.flat-with-text-lookbook {
  align-items: center;
}
.flat-with-text-lookbook .banner-img {
  position: relative;
}
.flat-with-text-lookbook .banner-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.flat-with-text-lookbook .banner-img .tf-pin-btn {
  position: absolute;
}
.flat-with-text-lookbook .banner-img .tf-pin-btn.pin-1 {
  top: 40%;
  left: 43%;
}
.flat-with-text-lookbook .banner-img .tf-pin-btn.pin-2 {
  top: 85%;
  left: 43%;
}
.flat-with-text-lookbook .lookbook-content .box-title {
  display: grid;
  gap: 12px;
  margin-bottom: 20px;
}
.flat-with-text-lookbook .lookbook-content .swiper {
  width: 100%;
}
.flat-with-text-lookbook .lookbook-content .total-lb {
  margin-top: 20px;
}

.flat-with-text-lookbook-v2 .banner-img {
  position: relative;
  width: 100%;
}
.flat-with-text-lookbook-v2 .banner-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.flat-with-text-lookbook-v2 .banner-img .tf-pin-btn {
  position: absolute;
}
.flat-with-text-lookbook-v2 .banner-img .tf-pin-btn.pin-1 {
  top: 42%;
  left: 25%;
}
.flat-with-text-lookbook-v2 .banner-img .tf-pin-btn.pin-2 {
  top: 30%;
  left: 44%;
}
.flat-with-text-lookbook-v2 .banner-img .tf-pin-btn.pin-3 {
  top: 62%;
  left: 80%;
}
.flat-with-text-lookbook-v2 .lookbook-content {
  padding: 0px 15px 40px;
  width: 100%;
}
.flat-with-text-lookbook-v2 .lookbook-content .box-title {
  display: grid;
  gap: 12px;
  margin-bottom: 20px;
}
.flat-with-text-lookbook-v2 .lookbook-content .swiper {
  width: 100%;
}
.flat-with-text-lookbook-v2 .lookbook-content .total-lb {
  margin-top: 20px;
}

.icv__circle {
  width: 56px;
  height: 36px;
  background-color: var(--white);
  border: 1px solid var(--main) !important;
  border-radius: 1000px;
}

.icv__arrow-wrapper {
  position: relative;
}
.icv__arrow-wrapper:first-child::before {
  position: absolute;
  font-family: "icomoon";
  content: "\e933";
  left: -2px;
  color: var(--main);
  font-size: 20px;
}
.icv__arrow-wrapper:last-child::before {
  position: absolute;
  font-family: "icomoon";
  content: "\e934";
  right: -2px;
  color: var(--main);
  font-size: 20px;
}
.icv__arrow-wrapper svg {
  display: none;
  width: 15px !important;
  height: 15px !important;
}
.icv__arrow-wrapper svg path {
  stroke: var(--main);
}

.icv__label {
  bottom: auto;
  top: 1rem;
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
  padding: 10px 24px;
  border-radius: 30px;
  background-color: var(--main);
}

@keyframes ripple-line {
  to {
    transform: scale(2);
    opacity: 0;
  }
}
.wrap-lookbook-hover .bundle-pin-item {
  cursor: pointer;
}
.wrap-lookbook-hover .bundle-hover-item {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.wrap-lookbook-hover .bundle-hover-item.no-hover {
  opacity: 0.3;
}
.wrap-lookbook-hover .bundle-hover-item.card-product .title {
  width: max-content;
  display: inline;
  background-repeat: no-repeat;
  background-position-y: 0px;
  background-image: linear-gradient(transparent calc(100% - 1px), currentColor 1px);
  transition: 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.wrap-lookbook-hover .has-hover .card-product:not(.no-hover) .title {
  background-size: 100% 100%;
  transition-delay: 0.2s;
}

/*------------ accordion ---------------- */
.accordion-product-wrap .accordion-product-item {
  padding: 20px 0;
}
.accordion-product-wrap .accordion-product-item:not(:last-child) {
  border-bottom: 1px solid var(--main);
}
.accordion-product-wrap .accordion-product-item .accordion-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.accordion-product-wrap .accordion-product-item .accordion-title:not(.collapsed) .btn-open-sub::before {
  transform: rotate(90deg);
}
.accordion-product-wrap .accordion-product-item .accordion-content {
  margin-top: 20px;
  padding: 40px;
  border-radius: 8px;
  border: 1px solid var(--line);
}
.accordion-product-wrap .btn-open-sub {
  position: relative;
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.accordion-product-wrap .btn-open-sub:after, .accordion-product-wrap .btn-open-sub::before {
  content: "";
  position: absolute;
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--main);
  transition: 0.4s ease 0.1s;
  margin: auto;
}
.accordion-product-wrap .btn-open-sub::before {
  width: 2px;
  height: 18px;
}
.accordion-product-wrap .btn-open-sub::after {
  width: 18px;
  height: 2px;
}
.accordion-product-wrap.style-faqs .accordion-product-item:first-child {
  padding-top: 0;
}
.accordion-product-wrap.style-faqs .accordion-product-item:last-child {
  padding-bottom: 0;
}
.accordion-product-wrap.style-faqs .accordion-faqs-content {
  padding-top: 8px;
}

/*------------ zoom ---------------- */
.tf-zoom-main {
  position: sticky;
  top: 30px;
  z-index: 50;
}
.tf-zoom-main .drift-zoom-pane {
  top: 0;
  left: 0;
  height: 520px;
  max-width: 520px;
  width: 100%;
  background: #fff;
  -webkit-transform: translate3d(0, 0, 0);
  box-shadow: 0 1px 5px rgba(127, 127, 127, 0.0196078431), 0 5px 18px rgba(127, 127, 127, 0.2);
  z-index: 3;
}

.drift-bounding-box.drift-open {
  background: rgba(255, 255, 255, 0.2509803922);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.4) inset;
  z-index: 5000;
}

.drift-zoom-pane {
  z-index: 5000;
}

.section-image-zoom .other-image-zoom {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.section-image-zoom.zoom-active .other-image-zoom {
  opacity: 0.3;
}

.pswp__bg {
  background: var(--white);
}

.pswp__icn {
  fill: rgb(34, 34, 34);
  color: var(--white);
}
.pswp__icn .pswp__icn-shadow {
  stroke: var(--white);
  stroke-width: 1px;
  fill: none;
}

.pswp__counter {
  color: #222;
  text-shadow: 1px 1px 3px #ffffff;
}

.tf-model-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}
.tf-model-viewer model-viewer {
  display: block;
  position: relative;
  z-index: 5;
  width: 100%;
  height: 100%;
}
.tf-model-viewer model-viewer.disabled {
  pointer-events: none;
}
.tf-model-viewer.active model-viewer {
  pointer-events: all;
}
.tf-model-viewer.active .wrap-btn-viewer {
  display: none;
}

/*------------ shop ---------------- */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  -ms-touch-action: none;
  touch-action: none;
  -ms-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-target {
  position: relative;
  direction: ltr;
}

.noUi-base,
.noUi-connects {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.noUi-connects {
  overflow: hidden;
  z-index: 0;
}

.noUi-connect,
.noUi-origin {
  will-change: transform;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}

html:not([dir=rtl]) .noUi-horizontal .noUi-origin {
  left: auto;
  right: 0;
}

.noUi-vertical .noUi-origin {
  width: 0;
}

.noUi-horizontal .noUi-origin {
  height: 0;
}

.noUi-handle {
  position: absolute;
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  -webkit-transition: transform 0.3s;
  transition: transform 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

.noUi-horizontal {
  height: 18px;
}

.noUi-horizontal .noUi-handle {
  width: 34px;
  height: 28px;
  left: -17px;
  top: -6px;
}

.noUi-vertical {
  width: 18px;
}

.noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  left: -6px;
  top: -17px;
}

html:not([dir=rtl]) .noUi-horizontal .noUi-handle {
  right: -17px;
  left: auto;
}

.noUi-draggable {
  cursor: ew-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}

.noUi-handle:after {
  left: 17px;
}

.noUi-vertical .noUi-handle:after,
.noUi-vertical .noUi-handle:before {
  width: 14px;
  height: 1px;
  left: 6px;
  top: 14px;
}

.noUi-vertical .noUi-handle:after {
  top: 17px;
}

[disabled] .noUi-connect {
  background: #b8b8b8;
}

[disabled] .noUi-handle,
[disabled].noUi-handle,
[disabled].noUi-target {
  cursor: not-allowed;
}

.noUi-pips,
.noUi-pips * {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-pips {
  position: absolute;
  color: #999;
}

.noUi-value {
  position: absolute;
  white-space: nowrap;
  text-align: center;
}

.noUi-value-sub {
  color: #ccc;
  font-size: 10px;
}

.noUi-marker {
  position: absolute;
  background: #ccc;
}

.noUi-marker-sub {
  background: #aaa;
}

.noUi-marker-large {
  background: #aaa;
}

.noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%;
}

.noUi-value-horizontal {
  -webkit-transform: translate(-50%, 50%);
  transform: translate(-50%, 50%);
}

.noUi-rtl .noUi-value-horizontal {
  -webkit-transform: translate(50%, 50%);
  transform: translate(50%, 50%);
}

.noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px;
}

.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px;
}

.noUi-marker-horizontal.noUi-marker-large {
  height: 15px;
}

.noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%;
}

.noUi-value-vertical {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%, 0);
  padding-left: 25px;
}

.noUi-rtl .noUi-value-vertical {
  -webkit-transform: translate(0, 50%);
  transform: translate(0, 50%);
}

.noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px;
}

.noUi-marker-vertical.noUi-marker-sub {
  width: 10px;
}

.noUi-marker-vertical.noUi-marker-large {
  width: 15px;
}

.noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center;
  white-space: nowrap;
}

.noUi-horizontal .noUi-tooltip {
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%;
}

.noUi-vertical .noUi-tooltip {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  top: 50%;
  right: 120%;
}

.noUi-horizontal {
  height: 4px;
}

.noUi-target {
  border: 0;
}

.noUi-base .noUi-connects {
  border-radius: 999px;
  background-color: var(--line);
}

.noUi-connect {
  background-color: var(--main);
}

.noUi-horizontal .noUi-handle,
.noUi-vertical .noUi-handle {
  height: 16px;
  width: 16px;
  border-radius: 50px;
  border: 2px solid var(--main);
  background-color: var(--white);
  box-shadow: unset;
  cursor: pointer;
}
.noUi-horizontal .noUi-handle::before, .noUi-horizontal .noUi-handle::after,
.noUi-vertical .noUi-handle::before,
.noUi-vertical .noUi-handle::after {
  content: none;
}

html:not([dir=rtl]) .noUi-horizontal .noUi-handle {
  right: -8px;
}

.tf-btn-filter {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  gap: 4px;
  color: var(--main);
  border: solid 2px var(--line);
  border-radius: 4px;
  text-transform: capitalize;
  padding: 3px 10px;
  font-weight: 400;
  background-color: var(--white);
  max-width: 100%;
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tf-btn-filter .icon {
  font-size: 20px;
}
.tf-btn-filter .icon-close {
  font-size: 14px;
}
.tf-btn-filter:hover {
  border-color: var(--main);
}
.tf-btn-filter.active {
  background-color: var(--main);
  border-color: var(--main);
  color: var(--white);
}

#filterDropdown {
  min-width: 93px;
}

.tf-shop-control {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  margin-bottom: 30px;
}
.tf-shop-control .tf-control-filter {
  display: flex;
  align-items: center;
  gap: 16px;
}
.tf-shop-control .tf-control-filter .shop-sale-text.active .icon {
  color: var(--success);
}
.tf-shop-control .shop-sale-text {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}
.tf-shop-control .shop-sale-text .icon {
  font-size: 24px;
  color: var(--secondary-2);
}
.tf-shop-control .tf-control-sorting {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}
.tf-shop-control .tf-control-layout {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}
.tf-shop-control .tf-control-layout .item {
  cursor: pointer;
}
.tf-shop-control .tf-control-layout .tf-view-layout-switch svg circle,
.tf-shop-control .tf-control-layout .tf-view-layout-switch svg rect {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-shop-control .tf-control-layout .tf-view-layout-switch.active svg circle,
.tf-shop-control .tf-control-layout .tf-view-layout-switch.active svg rect {
  fill: var(--main);
}

.wrapper-shop {
  transition: all 0.3s ease-in-out;
  animation: fadeShop 0.5s ease-in-out;
}

.wrapper-control-shop .tf-list-layout .card-product {
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--line);
}
.wrapper-control-shop .tf-list-layout .card-product.last-visible, .wrapper-control-shop .tf-list-layout .card-product:last-of-type {
  padding-bottom: 0;
  border: 0;
  margin-bottom: 0;
}
.wrapper-control-shop .tf-list-layout .load-more-btn,
.wrapper-control-shop .tf-list-layout .wg-pagination {
  margin-top: 30px;
}
.wrapper-control-shop .tf-grid-layout .wg-pagination {
  margin-top: 0;
}

@keyframes fadeShop {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.listLayout-wrapper #product-count-grid {
  display: none;
}
.listLayout-wrapper #product-count-list {
  display: block;
}

.gridLayout-wrapper #product-count-grid {
  display: block;
}
.gridLayout-wrapper #product-count-list {
  display: none;
}

.meta-filter-shop {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 30px;
}
.meta-filter-shop .count-text {
  font-size: 14px;
  line-height: 22px;
  color: var(--secondary);
  padding-right: 12px;
  position: relative;
}
.meta-filter-shop .count-text::after {
  position: absolute;
  top: 4px;
  bottom: 4px;
  right: 0;
  width: 1px;
  display: block;
  content: "";
  background-color: #d9d9d9;
}
.meta-filter-shop .count-text .count {
  color: var(--main);
  display: inline-block;
  margin-right: 2px;
}
.meta-filter-shop #applied-filters {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.meta-filter-shop .filter-tag {
  font-size: 14px;
  line-height: 22px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border: 1px solid var(--line);
  border-radius: 1000px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.meta-filter-shop .filter-tag .remove-tag {
  font-size: 12px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.meta-filter-shop .filter-tag:hover {
  border-color: var(--main);
}
.meta-filter-shop .color-tag {
  gap: 8px;
}
.meta-filter-shop .color-tag .color {
  width: 20px;
  height: 20px;
  display: block;
  border-radius: 1000px;
}
.meta-filter-shop .remove-all-filters {
  padding: 4px 12px;
  border: 1px solid var(--main);
}
.meta-filter-shop .remove-all-filters .icon {
  font-size: 12px;
}

.canvas-filter {
  max-width: 320px;
}
.canvas-filter .canvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  background-color: var(--surface);
  gap: 8px;
}
.canvas-filter .canvas-header .icon-close-popup {
  font-size: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.canvas-filter .canvas-header h5 {
  flex-grow: 1;
}
.canvas-filter .canvas-body {
  padding: 20px;
}

.widget-facet {
  padding-bottom: 20px;
}
.widget-facet:not(:last-child) {
  margin-bottom: 20px;
  border-bottom: 1px solid var(--line);
}
.widget-facet .facet-title {
  margin-bottom: 16px;
}
.widget-facet.facet-categories li:not(:last-child) {
  margin-bottom: 12px;
}
.widget-facet.facet-categories .categories-item.active, .widget-facet.facet-categories .categories-item:hover {
  color: var(--primary);
}
.widget-facet.facet-price .price-val-range {
  margin: 22px 0px;
}
.widget-facet.facet-price .box-price-product {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr 1fr;
}
.widget-facet.facet-price .title-price {
  margin-bottom: 4px;
  display: block;
}
.widget-facet.facet-price .price-val {
  padding: 5px 12px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  border-radius: 8px;
  border: 2px solid var(--line);
  position: relative;
}
.widget-facet.facet-price .price-val::after {
  content: attr(data-currency);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 12px;
  font-weight: 400;
  color: var(--secondary);
  font-size: 14px;
  line-height: 22px;
}
.widget-facet .facet-size-box {
  display: flex;
  flex-wrap: wrap;
  column-gap: 12px;
  row-gap: 16px;
  padding-right: 20px;
}
.widget-facet .facet-size-box .size-item {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border-radius: 1000px;
  border: 1px solid var(--line);
  color: var(--main);
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  text-transform: uppercase;
  cursor: pointer;
}
.widget-facet .facet-size-box .size-item:not(.free-size) {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.widget-facet .facet-size-box .size-item:hover {
  border-color: var(--main);
}
.widget-facet .facet-size-box .size-item.active {
  border-color: var(--main);
  color: var(--white);
  background-color: var(--main);
}
.widget-facet .facet-size-box .free-size {
  padding: 8px 16px;
  text-transform: capitalize;
}
.widget-facet .facet-color-box {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
.widget-facet .facet-color-box .color-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid var(--line);
  border-radius: 1000px;
  padding: 6px 12px 6px 8px;
  text-transform: capitalize;
}
.widget-facet .facet-color-box .color-item .color {
  width: 20px;
  height: 20px;
  border-radius: 1000px;
}
.widget-facet .facet-color-box .color-item:hover, .widget-facet .facet-color-box .color-item.active {
  border-color: var(--main);
}
.widget-facet.facet-fieldset .fieldset-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.widget-facet.facet-fieldset .fieldset-item:not(:last-child) {
  margin-bottom: 12px;
}
.widget-facet.facet-fieldset .fieldset-item label span {
  margin-left: 12px;
}

.sidebar-filter .facet-price .price-val-range {
  padding-left: 10px;
  padding-right: 10px;
}

.overlay-filter {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  width: 100vw;
  height: 100vh;
  background-color: var(--backdrop);
  visibility: hidden;
  opacity: 0;
}
.overlay-filter.show {
  opacity: 1;
  visibility: visible;
}

.wrapper-filter-dropdown {
  position: relative;
}

.sidebar-filter {
  background-color: var(--white);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.breadcrumbs-default {
  padding: 40px 0px 60px;
}
.breadcrumbs-default .breadcrumbs-content {
  display: grid;
  gap: 16px;
}
.breadcrumbs-default .breadcrumbs-content .content-bottom {
  display: grid;
  gap: 8px;
}

.tf-compare-table {
  overflow-x: scroll;
}
.tf-compare-table::-webkit-scrollbar {
  height: 4px;
}
.tf-compare-table::-webkit-scrollbar-thumb {
  background: var(--line);
}

.tf-compare-row {
  display: flex;
}
.tf-compare-row:nth-child(2n+2) > div {
  background-color: var(--surface);
}
.tf-compare-row .tf-compare-col:first-child {
  min-width: 240px;
}
.tf-compare-row:not(:first-child) .tf-compare-col:first-child {
  border-left: 1px solid var(--line);
}
.tf-compare-row:first-child .tf-compare-col:first-child {
  border: 0;
}
.tf-compare-row:first-child .tf-compare-col:not(:first-child) {
  border-top: 1px solid var(--line);
}
.tf-compare-row:first-child .tf-compare-col:nth-child(2) {
  border-left: 1px solid var(--line);
  border-top-left-radius: 8px;
}
.tf-compare-row:first-child .tf-compare-col:last-child {
  border-top-right-radius: 8px;
}
.tf-compare-row:nth-child(2) .tf-compare-col:first-child {
  border-top-left-radius: 8px;
  border-top: 1px solid var(--line);
}
.tf-compare-row:last-child .tf-compare-col:first-child {
  border-bottom-left-radius: 8px;
}
.tf-compare-row:last-child .tf-compare-col:last-child {
  border-bottom-right-radius: 8px;
}

.tf-compare-col {
  min-width: 200px;
  border-right: 1px solid var(--line);
  border-bottom: 1px solid var(--line);
  flex-grow: 1;
  position: relative;
}

.tf-compare-item {
  padding: 15px 20px;
}
.tf-compare-item .tf-compare-image {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  display: block;
}
.tf-compare-item .tf-compare-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.tf-compare-item .tf-compare-content {
  display: grid;
  margin-top: 16px;
  gap: 4px;
  text-align: center;
}

.tf-compare-field {
  padding: 15px;
}

.tf-compare-value {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tf-compare-stock {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  color: #83b735;
}
.tf-compare-stock .icon {
  width: 16px;
  height: 16px;
  background-color: #83b735;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tf-compare-stock .icon i {
  color: var(--white);
  font-size: 7px;
}

.btn-view-cart {
  padding: 3px 20px;
  border-radius: 8px;
  background-color: var(--main);
  color: var(--white);
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  text-transform: capitalize;
}
.btn-view-cart:hover {
  background-color: var(--primary);
}

.list-compare-color {
  display: flex;
  gap: 8px;
}
.list-compare-color .item {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid transparent;
}
.list-compare-color .item.active {
  border-color: var(--main);
}

.tf-compare-rate {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}
.tf-compare-rate .list-star {
  display: flex;
  align-items: center;
  gap: 2px;
}
.tf-compare-rate .list-star .icon {
  font-size: 14px;
  color: var(--yellow);
}
.tf-compare-rate .list-star .icon:last-child {
  color: var(--secondary-2);
}

.tf-cart-sold {
  margin-bottom: 36px;
}
.tf-cart-sold .notification-sold {
  margin-bottom: 20px;
  padding: 10px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.tf-cart-sold .notification-sold .icon {
  width: 24px;
  height: 30px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  animation: tf-ani-flash 2s infinite;
}
.tf-cart-sold .time-count {
  display: inline-block;
  font-weight: 600;
  color: var(--primary);
}
.tf-cart-sold .notification-progress .text {
  margin-bottom: 10px;
}
.tf-cart-sold .notification-progress .progress-cart {
  width: 100%;
  background-color: var(--line);
  height: 4px;
  position: relative;
  border-radius: 1000px;
}
.tf-cart-sold .notification-progress .progress-cart .value {
  position: relative;
  height: 100%;
  background-color: var(--success);
  transition: width 2s ease;
}
.tf-cart-sold .notification-progress .progress-cart .round {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: var(--success);
  border-radius: 1000px;
}

@keyframes tf-ani-flash {
  50%, from, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
.tf-table-page-cart {
  border-spacing: 0;
  border-collapse: collapse;
  margin-bottom: 28px;
  width: 100%;
  line-height: 1.4;
}
.tf-table-page-cart tr {
  border-bottom: 1px solid var(--line);
}
.tf-table-page-cart th {
  padding-bottom: 12px;
  font-weight: 500;
  padding-left: 10px;
  padding-right: 10px;
}
.tf-table-page-cart th:last-child {
  padding-right: 0;
}
.tf-table-page-cart th:first-child {
  padding-left: 0;
}
.tf-table-page-cart td {
  padding: 18px 10px;
  align-content: center;
}
.tf-table-page-cart td:last-child {
  padding-right: 0;
  padding-left: 0;
}
.tf-table-page-cart td:first-child {
  padding-left: 0;
}

.tf-cart-sold .notification-sold {
  padding: 10px;
}

.tf-cart-item .tf-cart-item_product {
  display: flex;
  align-items: center;
}
.tf-cart-item .tf-cart-item_product .img-box {
  border-radius: 4px;
  overflow: hidden;
  width: 90px;
  height: 120px;
  margin-right: 24px;
  display: block;
}
.tf-cart-item .tf-cart-item_product .img-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.tf-cart-item .tf-cart-item_product .cart-info {
  display: grid;
  gap: 12px;
}
.tf-cart-item .tf-cart-item_product .variant-box {
  display: grid;
  gap: 8px;
  grid-template-columns: 1fr 1fr;
}
.tf-cart-item .tf-cart-item_product .tf-select select {
  min-width: 100px;
  border-radius: 8px;
  padding: 5px 12px;
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
}
.tf-cart-item .tf-cart-item_product .tf-select::after {
  right: 12px;
  font-size: 16px;
}
.tf-cart-item .wg-quantity {
  width: 120px;
  height: 40px;
}
.tf-cart-item .wg-quantity input {
  pointer-events: none;
  height: 38px;
  width: 40px;
}
.tf-cart-item .wg-quantity .btn-quantity {
  width: 40px;
  height: 30px;
  font-size: 30px;
}
.tf-cart-item .tf-cart-item_price .old-price {
  display: inline-block;
  margin-right: 8px;
  font-size: 14px;
  line-height: 22px;
  color: var(--secondary-2);
}
.tf-cart-item .remove-cart .icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  background-color: var(--primary);
  border: 1px solid var(--critical);
  border-radius: 1000px;
  font-size: 8px;
  color: var(--critical);
  background-color: var(--white);
  cursor: pointer;
}
.tf-cart-item .remove-cart .icon:hover {
  background-color: var(--critical);
  color: var(--white);
}

.ip-discount-code {
  position: relative;
}
.ip-discount-code .tf-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  bottom: 8px;
  border-radius: 8px;
  padding: 6px 20px;
}
.ip-discount-code input {
  padding: 12px 20px;
  padding-right: 140px;
}

.group-discount {
  margin-top: 28px;
  display: flex;
  gap: 20px;
  overflow-x: auto;
}
.group-discount::-webkit-scrollbar {
  height: 4px;
}
.group-discount::-webkit-scrollbar-thumb {
  background-color: var(--main);
}
.group-discount::-webkit-scrollbar-track {
  background-color: var(--line);
}
.group-discount .box-discount {
  flex-shrink: 0;
}

.box-discount {
  width: 220px;
  border-radius: 4px;
  position: relative;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  overflow: hidden;
}
.box-discount .discount-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid var(--line);
  border-bottom-style: dashed;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.box-discount .discount-bot {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid var(--line);
  border-top: none;
  padding: 8px 12px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.box-discount .discount-bot .tf-btn {
  font-size: 12px;
  line-height: 20px;
  font-weight: 600;
  padding: 2px 8px;
}
.box-discount::before, .box-discount::after {
  position: absolute;
  z-index: 5;
  content: "";
  top: 56px;
  background-color: var(--white);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  border: 1px solid var(--line);
}
.box-discount::before {
  left: -7px;
}
.box-discount::after {
  right: -7px;
}
.box-discount:hover, .box-discount.active {
  background-color: var(--rgba-primary);
}
.box-discount:hover .discount-bot,
.box-discount:hover .discount-top, .box-discount.active .discount-bot,
.box-discount.active .discount-top {
  border-color: var(--primary);
}
.box-discount:hover::before, .box-discount:hover::after, .box-discount.active::before, .box-discount.active::after {
  border-color: var(--primary);
}

.fl-sidebar-cart {
  position: sticky;
  top: 90px;
}

.box-order {
  border-radius: 12px;
  overflow: hidden;
  padding: 15px;
}
.box-order .title {
  margin-bottom: 28px;
}
.box-order .subtotal,
.box-order .discount,
.box-order .ship {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--line);
}
.box-order .ship {
  display: flex;
  gap: 30px;
}
.box-order .ship-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.box-order .ship-item:not(:last-child) {
  margin-bottom: 4px;
}
.box-order .ship-item label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
  color: var(--secondary);
}
.box-order .ship-item input:checked ~ label {
  color: var(--main);
}
.box-order .box-progress-checkout {
  display: grid;
  gap: 12px;
}
.box-order .check-agree {
  display: flex;
  gap: 8px;
  align-items: center;
}
.box-order .check-agree label a {
  font-weight: 600;
  text-decoration: underline;
  text-transform: capitalize;
}
.box-order .total-order {
  margin-bottom: 20px;
}
.box-order .tf-btn {
  padding: 10px 32px;
}

.line-separation {
  width: 100%;
  height: 1px;
  background-color: var(--line);
  margin: auto;
}

.tf-page-checkout .wrap:not(:last-child) {
  margin-bottom: 30px;
}
.tf-page-checkout .wrap .title {
  margin-bottom: 20px;
}
.tf-page-checkout .title-login {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 11px 16px;
  border-radius: 8px;
  background-color: var(--surface);
  color: var(--secondary-2);
  margin-bottom: 12px;
}
.tf-page-checkout .title-login a {
  border-bottom: 1px solid;
}
.tf-page-checkout .login-box {
  padding: 15px;
  border: 1px solid var(--line);
  border-radius: 8px;
}
.tf-page-checkout .login-box .grid-2 {
  gap: 15px;
  margin-bottom: 12px;
}
.tf-page-checkout .login-box input {
  border-radius: 4px;
}
.tf-page-checkout .login-box .tf-btn {
  padding: 10px 32px;
}
.tf-page-checkout .info-box {
  display: grid;
  gap: 15px;
}
.tf-page-checkout .info-box .grid-2 {
  gap: 16px;
}
.tf-page-checkout .tf-select select {
  border-radius: 8px;
  padding: 10px 16px;
}
.tf-page-checkout textarea {
  height: 100px;
}

.payment-box .payment-item {
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid var(--line);
}
.payment-box .payment-item .payment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.payment-box .payment-item .payment-header span {
  flex-grow: 1;
}
.payment-box .payment-item .payment-header input:checked {
  border-color: #2e72d2;
}
.payment-box .payment-item .payment-header input:checked::before {
  background-color: #2e72d2;
}
.payment-box .payment-item .payment-body {
  padding: 20px;
  padding-top: 0;
  display: grid;
  gap: 15px;
}
.payment-box .payment-item .payment-body .check-save {
  display: flex;
  gap: 8px;
  align-items: center;
}
.payment-box .payment-item .payment-body .input-payment-box {
  display: grid;
  gap: 15px;
}
.payment-box .payment-item .payment-body .grid-2 {
  gap: 16px;
}
.payment-box .payment-item .payment-body .ip-card {
  position: relative;
}
.payment-box .payment-item .payment-body .ip-card .list-card {
  display: flex;
  gap: 12px;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.payment-box .payment-item:not(:last-child) {
  margin-bottom: 15px;
}
.payment-box .payment-item .apple-pay-title {
  display: flex;
  gap: 8px;
  align-items: center;
}
.payment-box .payment-item .apple-pay-title img {
  width: 13px;
  height: 16px;
}
.payment-box .payment-item .paypal-title {
  display: flex;
}
.payment-box .payment-item .paypal-title img {
  width: 60px;
  height: 16px;
}
.payment-box .payment-choose-card.active {
  border-color: transparent;
  background-color: var(--surface);
}
.payment-box .payment-choose-card.active .payment-header {
  padding-top: 20px;
  padding-bottom: 8px;
}
.payment-box .paypal-item .payment-header {
  padding: 16px 20px;
}

.form-payment .tf-btn {
  margin-top: 30px;
  padding: 10px 32px;
  width: 100%;
}

.flat-sidebar-checkout {
  position: sticky;
  top: 0px;
}

.sidebar-checkout-content {
  padding-top: 10px;
}
.sidebar-checkout-content .sec-discount,
.sidebar-checkout-content .list-product,
.sidebar-checkout-content .title {
  margin-bottom: 32px;
}
.sidebar-checkout-content .list-product .item-product {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--line);
}
.sidebar-checkout-content .list-product .item-product:last-child {
  margin-bottom: 0;
}
.sidebar-checkout-content .item-product {
  display: flex;
  align-items: center;
  gap: 15px;
}
.sidebar-checkout-content .item-product .img-product {
  width: 90px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;
}
.sidebar-checkout-content .item-product .img-product img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.sidebar-checkout-content .item-product .content-box {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}
.sidebar-checkout-content .item-product .content-box .info {
  display: grid;
  gap: 8px;
}
.sidebar-checkout-content .item-product .content-box .info .name-product {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.sidebar-checkout-content .item-product .content-box .total-price {
  display: flex;
  gap: 4px;
}
.sidebar-checkout-content .sec-discount .box-discount {
  width: 100%;
}
.sidebar-checkout-content .sec-discount .ip-discount-code {
  margin-top: 20px;
  margin-bottom: 12px;
}
.sidebar-checkout-content .sec-total-price .top {
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--line);
  border-top: 1px solid var(--line);
  margin-bottom: 20px;
}
.sidebar-checkout-content .sec-total-price .top .item:not(:last-child) {
  margin-bottom: 16px;
}

.tracking-wrap .form-login .wrap {
  gap: 16px;
}

.canvas-filter .widget-facet:last-child {
  padding-bottom: 0;
}
.canvas-filter .canvas-bottom {
  box-shadow: var(--shadow2);
  padding: 12px 20px;
}
.canvas-filter .canvas-bottom .tf-btn {
  width: 100%;
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
}

/*------------ sections ---------------- */
.heading-section {
  margin-bottom: 40px;
}
.heading-section h3,
.heading-section .heading {
  margin-bottom: 12px;
  text-transform: capitalize;
}
.heading-section p {
  color: #64666c;
}
.heading-section.type-space-2 {
  margin-bottom: 28px;
}

.heading-section-2 {
  margin-bottom: 44px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  flex-wrap: wrap;
}
.heading-section-2.type-2 {
  margin-bottom: 40px;
}

.heading-section-3 {
  margin-bottom: 30px;
}
.heading-section-3 h2 {
  margin-bottom: 12px;
}

.heading-section-4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 40px;
}
.heading-section-4 .heading-left {
  display: flex;
  align-items: center;
  gap: 15px 60px;
  flex-wrap: wrap;
  max-width: 100%;
}
.heading-section-4.style-2 {
  margin-bottom: 30px;
}

.page-title {
  padding: 60px 0 80px;
  background: var(--gradient);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.page-title .heading {
  margin-bottom: 12px;
}

ul.breadcrumbs {
  display: flex;
  gap: 4px;
  align-items: center;
}
ul.breadcrumbs li {
  color: var(--secondary-2);
  font-size: 14px;
  line-height: 22px;
}
ul.breadcrumbs i {
  font-size: 12px;
  color: var(--secondary-2);
}

.main-content-page {
  padding: 80px 0;
}

.blog-detail-wrap.page-single-2 > .inner {
  margin: 0;
  max-width: unset;
  padding: 0;
}
.blog-detail-wrap.page-single-2 > .inner > .heading {
  text-align: start;
}
.blog-detail-wrap.page-single-2 > .inner > .image {
  border-radius: 12px;
  overflow: hidden;
}
.blog-detail-wrap > .image {
  height: 568px;
  background-image: url(../images/blog/blog-details-1.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
}
.blog-detail-wrap > .inner {
  max-width: 970px;
  margin: -135px auto 0;
  border-radius: 12px;
  background-color: var(--white);
  padding: 40px 60px 0px 60px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.blog-detail-wrap > .inner > .heading {
  display: flex;
  flex-direction: column;
  gap: 16px;
  text-align: center;
}
.blog-detail-wrap > .inner > .heading ul.tags {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}
.blog-detail-wrap > .inner > .heading ul.tags a {
  display: flex;
  border-radius: 40px;
  padding: 4px 16px 4px 16px;
  background-color: var(--surface);
}
.blog-detail-wrap > .inner > .heading .meta .icon {
  font-size: 20px;
}
.blog-detail-wrap > .inner .related-post {
  position: relative;
  display: flex;
  gap: 40px;
  padding: 24px 0 32px;
  border-top: 1px solid var(--line);
  border-bottom: 1px solid var(--line);
}
.blog-detail-wrap > .inner .related-post::after {
  position: absolute;
  content: "";
  width: 1px;
  height: 60px;
  background-color: var(--line);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.blog-detail-wrap > .inner .related-post .text-btn-uppercase {
  font-weight: 600;
  letter-spacing: 0.1em;
  margin-bottom: 4px;
}
.blog-detail-wrap > .inner .related-post .text-btn-uppercase a {
  color: var(--primary);
}
.blog-detail-wrap > .inner .related-post .text-btn-uppercase a:hover {
  color: var(--main);
}

ul.list-text {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
ul.list-text li {
  position: relative;
  padding-left: 20px;
}
ul.list-text.type-disc li:before {
  position: absolute;
  content: "";
  top: 13px;
  left: 13px;
  width: 2.5px;
  height: 2.5px;
  border-radius: 50%;
  background-color: var(--main);
}
ul.list-text.type-number {
  list-style-type: auto;
  margin-bottom: 0;
  padding-left: 0;
  list-style: auto;
}

.reply-comment .reply-comment-heading {
  margin-bottom: 24px;
}
.reply-comment .reply-comment-item {
  display: flex;
  gap: 20px;
}
.reply-comment .reply-comment-item:not(:last-child) {
  margin-bottom: 20px;
}
.reply-comment .reply-comment-item:not(:last-child) .content {
  border-bottom: 1px solid var(--line);
}
.reply-comment .reply-comment-item.type-reply {
  padding-left: 80px;
}
.reply-comment .reply-comment-item .image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
}
.reply-comment .reply-comment-item .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.reply-comment .reply-comment-item .content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 20px;
}
.reply-comment .reply-comment-item .content .text-button {
  color: var(--primary);
}
.reply-comment.style-1 .reply-comment-item {
  flex-direction: column;
  gap: 12px;
}
.reply-comment.style-1 .reply-comment-item .user {
  display: flex;
  align-items: center;
  gap: 16px;
}
.reply-comment.style-1 .reply-comment-item h6 {
  margin-bottom: 2px;
}
.reply-comment.style-1 .reply-comment-item:not(:last-child) {
  margin-bottom: 24px;
}
.reply-comment.style-1 .type-reply {
  margin-left: 60px;
  border-left: 4px solid var(--line);
  padding-left: 16px;
}
.reply-comment.style-1 .type-reply .image {
  width: 52px;
  height: 52px;
}

.box-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--success);
}

.leave-comment .leave-comment-heading {
  margin-bottom: 24px;
}

.tf-breadcrumb-wrap {
  padding: 21px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
}
.tf-breadcrumb-wrap .tf-breadcrumb-list {
  display: flex;
  gap: 4px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 1px;
}
.tf-breadcrumb-wrap .tf-breadcrumb-list .icon {
  font-size: 12px;
}
.tf-breadcrumb-wrap .tf-breadcrumb-list span {
  text-decoration: underline;
}
.tf-breadcrumb-wrap .tf-breadcrumb-prev-next {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 23px;
}
.tf-breadcrumb-wrap .tf-breadcrumb-prev-next a {
  display: flex;
}

.list-star {
  white-space: nowrap;
}

.list-star-default {
  white-space: nowrap;
  display: flex;
  gap: 2px;
}
.list-star-default .icon {
  font-size: 14px;
  color: var(--yellow);
}
.list-star-default.color-primary .icon {
  color: var(--primary);
}

.write-cancel-review-wrap .write-review-wrap,
.write-cancel-review-wrap .btn-cancel-review {
  display: none;
}
.write-cancel-review-wrap.write-review .cancel-review-wrap,
.write-cancel-review-wrap.write-review .btn-write-review {
  display: none;
}
.write-cancel-review-wrap.write-review .write-review-wrap,
.write-cancel-review-wrap.write-review .btn-cancel-review {
  display: block;
}
.write-cancel-review-wrap.write-review .check-save label {
  margin-left: 8px;
}

.tf-countdown.style-1 .countdown__timer {
  display: flex;
  gap: 47px;
}
.tf-countdown.style-1 .countdown__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
  position: relative;
}
.tf-countdown.style-1 .countdown__item .countdown__value {
  font-size: 24px;
  font-weight: 500;
  line-height: 30px;
  margin-bottom: -4px;
}
.tf-countdown.style-1 .countdown__item .countdown__label {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: var(--secondary);
}
.tf-countdown.style-1 .countdown__item:not(:last-child)::after {
  position: absolute;
  content: ":";
  font-size: 24px;
  font-weight: 500;
  line-height: 30px;
  right: -26px;
  top: 50%;
  transform: translateY(-50%);
}
.tf-countdown.style-2 .countdown__timer {
  display: flex;
  gap: 64px;
  justify-content: center;
  align-items: center;
}
.tf-countdown.style-2 .countdown__timer .countdown__item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 68px;
  height: 68px;
}
.tf-countdown.style-2 .countdown__timer .countdown__item .countdown__value {
  font-size: 40px;
  font-weight: 500;
  line-height: 48px;
  color: var(--white);
}
.tf-countdown.style-2 .countdown__timer .countdown__item .countdown__label {
  color: var(--white);
  position: absolute;
  left: 60px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #28513b;
}
.tf-countdown.style-2 .countdown__timer .countdown__item::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 50%;
  border: 2px dashed var(--white);
  animation: rotate-360 5s infinite linear;
}

.tf-countdown-lg .countdown__timer {
  display: flex;
  gap: 40px;
}
.tf-countdown-lg .countdown__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.tf-countdown-lg .countdown__item .countdown__value {
  font-size: 28px;
  font-weight: 500;
  line-height: 36px;
}
.tf-countdown-lg .countdown__item .countdown__label {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: var(--secondary);
}
.tf-countdown-lg .countdown__item:not(:last-child)::after {
  position: absolute;
  content: ":";
  font-size: 28px;
  font-weight: 500;
  line-height: 36px;
  top: 50%;
  right: -22px;
  transform: translateY(-50%);
}
.tf-countdown-lg.style-1 {
  margin-bottom: 28px;
}
.tf-countdown-lg.style-1 .countdown__timer {
  gap: 15px;
}
.tf-countdown-lg.style-1 .countdown__item {
  background-color: var(--white);
  border-radius: 8px;
  padding: 8px;
  display: flex;
  align-items: center;
}
.tf-countdown-lg.style-1 .countdown__item:not(:last-child)::after {
  right: -10px;
}

.flat-countdown-banner {
  position: relative;
}
.flat-countdown-banner .banner-img {
  position: absolute;
  top: 0;
  bottom: 0;
  left: auto;
  right: 0;
}
.flat-countdown-banner .banner-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.flat-countdown-banner .banner-left {
  display: grid;
  gap: 24px;
}
.flat-countdown-banner .box-title {
  display: grid;
  gap: 12px;
}

.flat-countdown-banner-2 {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.flat-countdown-banner-2 .box-content .box-title {
  display: grid;
  gap: 12px;
}
.flat-countdown-banner-2 .box-content .tf-countdown-lg {
  margin-top: 15px;
}
.flat-countdown-banner-2 .box-content .btn-banner {
  margin-top: 30px;
}

.flat-banner-parallax {
  background-attachment: fixed;
  position: relative;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 100px 0px;
}
.flat-banner-parallax .fl-content {
  display: grid;
  gap: 30px;
}
.flat-banner-parallax .fl-content .title-top {
  display: grid;
  gap: 12px;
}

.flat-banner-parallax-v2 {
  background-attachment: fixed;
  position: relative;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 133px 0px;
}
.flat-banner-parallax-v2 .fl-content {
  display: grid;
  gap: 32px;
  padding: 30px 15px;
  background-color: var(--white);
  border-radius: 8px;
}
.flat-banner-parallax-v2 .fl-content .title-top .subtitle {
  margin-bottom: 4px;
}
.flat-banner-parallax-v2 .fl-content .title-top .title {
  margin-bottom: 16px;
}

.flat-banner-parallax-v3 {
  background-attachment: fixed;
  position: relative;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 110.5px 0px;
}
.flat-banner-parallax-v3 .fl-content {
  display: grid;
  gap: 28px;
  padding: 30px 15px;
  background-color: var(--white);
  border-radius: 8px;
}
.flat-banner-parallax-v3 .fl-content .title-top p {
  margin-bottom: 24px;
}
.flat-banner-parallax-v3 .fl-content .title-top .title {
  margin-bottom: 12px;
}

.gallery-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.gallery-item .box-icon {
  position: absolute;
  z-index: 5;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: var(--white);
  width: 32px;
  height: 32px;
  font-size: 18px;
}
.gallery-item .box-icon:hover {
  background-color: var(--main);
  color: var(--white);
}
.gallery-item .gallery-content {
  position: absolute;
  top: 50%;
  left: 15px;
  right: 15px;
  transform: translateY(-50%);
  text-align: center;
  z-index: 123;
}
.gallery-item .gallery-content .cls-btn {
  background-color: var(--white);
  color: var(--main);
  border-radius: 99px;
  padding: 8px 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  max-width: 200px;
  width: 100%;
}
.gallery-item .gallery-content .cls-btn .icon {
  transform: scale(0);
  transform-origin: right;
  transition: all 0.2s ease;
  color: var(--primary);
  width: 0;
  display: inline-block;
  font-size: 20px;
}
.gallery-item .gallery-content .cls-btn .text {
  color: inherit;
  z-index: 1;
}
.gallery-item .gallery-content .cls-btn:hover {
  color: var(--primary);
}
.gallery-item .gallery-content .cls-btn:hover .icon {
  width: 10px;
  min-width: 10px;
  margin-left: 4px;
  transform: scale(1);
}

.tf-marquee {
  display: flex;
  overflow: hidden;
  width: 100%;
  padding: 16px 0;
  border-top: 1px solid var(--line);
  border-bottom: 1px solid var(--line);
}
.tf-marquee .marquee-wrapper {
  display: flex;
  animation: infiniteScroll 30s linear infinite;
  align-items: center;
  transition: animation-duration 300ms;
}
.tf-marquee .marquee-wrapper:hover {
  animation-play-state: paused !important;
}
.tf-marquee .marquee-child-item {
  padding-left: 15px;
  padding-right: 15px;
  display: inline-flex;
}
.tf-marquee .marquee-child-item .icon {
  font-size: 16px;
}
.tf-marquee.marquee-white {
  border-color: transparent;
}
.tf-marquee.marquee-white .marquee-child-item {
  color: var(--white);
}
.tf-marquee.marquee-style2 {
  padding: 12px 0px;
  border: 0;
}
.tf-marquee.marquee-style2 .marquee-wrapper {
  animation-duration: 80s;
}
.tf-marquee.marquee-style2 .marquee-child-item h3 {
  font-weight: 600;
}
.tf-marquee.marquee-style2 .marquee-child-item .icon {
  font-size: 30px;
  color: var(--primary);
}
.tf-marquee.marquee-style3 {
  padding: 4px 0px;
}
.tf-marquee.marquee-style3 .marquee-wrapper {
  animation-duration: 80s;
}
.tf-marquee.marquee-style4 {
  padding: 12px 0px;
  border: 0;
}
.tf-marquee.marquee-animation-right .marquee-wrapper {
  animation: infiniteScrollRight 80s linear infinite;
}

.clip-text {
  color: white;
  -webkit-text-stroke: 1px #181818;
}

.flat-img-with-text {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 0px;
}
.flat-img-with-text .banner-content {
  padding: 30px;
  background-color: var(--surface);
  z-index: 5;
  text-align: center;
}
.flat-img-with-text .banner-content .content-text {
  margin-bottom: 20px;
  display: grid;
  gap: 8px;
}
.flat-img-with-text .banner {
  width: 100%;
}
.flat-img-with-text .banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.flat-img-with-text .banner-right {
  padding-top: 0px;
}
.flat-img-with-text .banner-left {
  padding-bottom: 0px;
  padding-right: 0px;
}

.flat-img-with-text-v2 .banner-left {
  margin-bottom: 30px;
  display: grid;
  gap: 30px;
}
.flat-img-with-text-v2 .banner-left .box-title {
  display: grid;
  gap: 12px;
}
.flat-img-with-text-v2 .banner-left .box-title p {
  color: var(--secondary);
}
.flat-img-with-text-v2 .banner-right .collection-position-2 {
  border-radius: 8px;
}

.flat-img-with-text-v3 {
  align-items: center;
}
.flat-img-with-text-v3 .box-title {
  margin-bottom: 20px;
  display: grid;
  gap: 8px;
}
.flat-img-with-text-v3 .collection-default {
  gap: 16px;
}
.flat-img-with-text-v3 .collection-default .img-style {
  border-radius: 4px;
  overflow: hidden;
}
.flat-img-with-text-v3 .collection-default .content {
  gap: 4px;
}

.flat-img-with-text-v4 {
  display: grid;
  gap: 40px;
}
.flat-img-with-text-v4 .relatest-post .relatest-post-item {
  align-items: flex-start;
}

.dropdown .dropdown-title {
  cursor: pointer;
}
.dropdown .dropdown-backdrop.show:before {
  position: fixed;
  content: "";
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(24, 24, 24, 0.2);
  z-index: 200;
}
.dropdown.dropdown-store-location {
  width: max-content;
}
.dropdown.dropdown-store-location .dropdown-title {
  cursor: pointer;
  width: max-content;
}
.dropdown.dropdown-store-location .dropdown-menu {
  border: 0;
  background-color: transparent;
  padding: 0;
}
.dropdown.dropdown-store-location .dropdown-content {
  box-shadow: 0px 10px 25px 0px rgba(43, 52, 74, 0.12);
  width: 360px;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid var(--line);
  background-color: var(--white);
  margin: 17px 0 !important;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dropdown.dropdown-store-location .dropdown-content-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dropdown.dropdown-store-location .dropdown-content-heading .icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  cursor: pointer;
}

.about-us-main {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.about-us-main .about-us-features {
  border-radius: 8px;
  overflow: hidden;
}
.about-us-main .about-us-features img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.about-us-main .about-us-content {
  padding: 15px 0;
}
.about-us-main .about-us-content .title {
  margin-bottom: 20px;
}
.about-us-main .about-us-content .widget-tabs {
  margin-bottom: 32px;
}

.team-item {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.team-item .image {
  border-radius: 12px;
  overflow: hidden;
}
.team-item .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.team-item .content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}
.team-item .content .name a {
  display: inline;
  background-repeat: no-repeat;
  background-position-y: 0px;
  background-image: linear-gradient(transparent calc(100% - 1px), currentColor 1px);
  transition: 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.team-item .name {
  margin-bottom: 4px;
}
.team-item .tf-social-icon a {
  width: 48px;
  height: 48px;
  border-color: var(--line);
}
.team-item:hover .name a {
  background-size: 100% 100%;
  transition-delay: 0.2s;
  font-weight: 600;
}

.tf-store-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 850px;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
}
.tf-store-list::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
.tf-store-list::-webkit-scrollbar-thumb {
  background: var(--line);
}
.tf-store-list::-webkit-scrollbar-track {
  background: transparent;
}
.tf-store-list .tf-store-item {
  padding: 22px;
  border-radius: 8px;
  border: 2px solid var(--line);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 10px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-store-list .tf-store-item .tf-store-contact {
  display: flex;
  gap: 10px 60px;
  flex-wrap: wrap;
}
.tf-store-list .tf-store-item .tf-store-contact .tf-store-info {
  width: max-content;
}
.tf-store-list .tf-store-item.active, .tf-store-list .tf-store-item:hover {
  border-color: var(--main);
}
.tf-store-list.style-row {
  flex-direction: row;
  overflow-x: auto;
  max-height: unset;
  padding-right: 0px;
  margin-right: 0px;
  padding-bottom: 8px;
  margin-bottom: -8px;
  margin-bottom: 80px;
}
.tf-store-list.style-row .tf-store-item {
  width: 310px;
  flex-shrink: 0;
  gap: 12px;
}
.tf-store-list.style-row .tf-store-item .tf-store-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.map-contact {
  height: 850px;
  border-radius: 8px;
}
.map-contact .map-marker-container {
  position: absolute;
  margin-top: 10px;
  transform: translate3d(-50%, -100%, 0);
}
.map-contact .marker-container {
  position: relative;
  top: 25px;
  left: 10px;
  width: 46px;
  height: 46px;
  z-index: 1;
  border-radius: 50%;
  cursor: pointer;
  -webkit-perspective: 1000;
}
.map-contact .marker-card .face {
  position: absolute;
  width: 28px;
  height: 28px;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  text-align: center;
  color: #fff;
  z-index: 100;
  border: 8px solid #fff;
  border-radius: 50%;
  box-sizing: content-box;
  background-clip: content-box;
  line-height: 46px;
  font-size: 24px;
  background: none;
  border: none;
}
.map-contact .marker-card .face::before, .map-contact .marker-card .face::after {
  content: none;
}
.map-contact .marker-card .face div {
  background-color: var(--white);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  position: relative;
}
.map-contact .marker-card .face div::after {
  position: absolute;
  inset: 0;
  content: "\e905";
  font-family: "icomoon";
  font-size: 28px;
  color: var(--primary);
}
.map-contact .marker-card .face div::before {
  position: absolute;
  content: "";
  width: 60px;
  height: 60px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(228, 49, 49, 0.1);
  border-radius: 50%;
}
.map-contact.h400 {
  height: 400px;
  border-radius: 0;
}
.map-contact.h520 {
  height: 520px;
  border-radius: 0;
}

.wg-card-store {
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--surface);
}
.wg-card-store .card-store-img {
  width: 100%;
  height: 100%;
}
.wg-card-store .card-store-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.wg-card-store .card-store-info {
  padding: 30px 80px 30px 100px;
}
.wg-card-store .card-store-info .card-store-heading {
  margin-bottom: 44px;
}
.wg-card-store .card-store-info > ul {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 60px;
}
.wg-card-store .card-store-info .tf-social-icon {
  gap: 15px;
}

.contact-us-content {
  display: flex;
  gap: 60px;
}
.contact-us-content .left {
  flex-grow: 1;
}
.contact-us-content .left > h4 {
  margin-bottom: 7px;
}
.contact-us-content .left > p {
  margin-bottom: 24px;
}
.contact-us-content .right {
  width: 100%;
  max-width: 356px;
}
.contact-us-content .right h4 {
  margin-bottom: 30px;
}
.contact-us-content .right .open-time {
  display: flex;
  gap: 4px;
}
.contact-us-content .right .open-time span {
  width: 80px;
}

.contact-us-map {
  display: flex;
  background-color: var(--surface);
  border-radius: 12px;
  overflow: hidden;
}
.contact-us-map .wrap-map {
  flex-grow: 1;
  height: 500px;
}
.contact-us-map .wrap-map .map-contact {
  border-radius: 0;
  height: 500px;
}
.contact-us-map .right {
  width: 100%;
  max-width: 470px;
  padding: 40px;
}

.page-404 {
  min-height: 100vh;
  display: flex;
  align-items: center;
}
.page-404 .page-404-inner {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  align-items: center;
}
.page-404 .page-404-inner .content {
  max-width: 450px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.page-404 .page-404-inner .content .heading {
  font-size: 160px;
  font-weight: 700;
  line-height: 198.44px;
}
.page-404 .page-404-inner .content a {
  width: max-content;
}

.coming-soon {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-image: url(../images/section/coming-soon.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.coming-soon .coming-soon-inner {
  flex-grow: 1;
  max-width: 1370px;
  padding: 0 15px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  justify-content: end;
}
.coming-soon .coming-soon-inner .content {
  max-width: 612px;
  text-align: center;
}
.coming-soon .coming-soon-inner .content .heading {
  color: var(--white);
  margin-bottom: 60px;
}
.coming-soon .coming-soon-inner .content .js-countdown {
  margin-bottom: 24px;
}
.coming-soon .coming-soon-inner .content .js-countdown .countdown__timer {
  justify-content: center;
}
.coming-soon .coming-soon-inner .content .js-countdown .countdown__item::after,
.coming-soon .coming-soon-inner .content .js-countdown .countdown__label,
.coming-soon .coming-soon-inner .content .js-countdown .countdown__value {
  color: var(--white);
}
.coming-soon .coming-soon-inner .content form {
  margin-bottom: 24px;
}
.coming-soon .coming-soon-inner .content form input {
  height: 52px;
  border-radius: 8px;
}
.coming-soon .coming-soon-inner .content form button {
  width: 44px;
  height: 44px;
  border-radius: 8px;
}
.coming-soon .coming-soon-inner .content form button:hover svg path {
  stroke: var(--main);
}

.page-faqs-wrap {
  display: flex;
  gap: 60px;
}
.page-faqs-wrap .list-faqs {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 60px;
}
.page-faqs-wrap .list-faqs .faqs-title {
  margin-bottom: 20px;
}
.page-faqs-wrap .ask-question {
  width: 380px;
  height: max-content;
  flex-shrink: 0;
  box-shadow: 0px 10px 25px 0px rgba(43, 52, 74, 0.12);
  border-radius: 16px;
  padding: 24px;
}
.page-faqs-wrap .ask-question .ask-question-wrap form .tf-select select {
  padding: 9px 16px;
  border-radius: 4px;
}
.page-faqs-wrap .ask-question .ask-question-wrap form textarea {
  height: 86px;
}
.page-faqs-wrap .ask-question .ask-question-wrap form button {
  height: 48px;
  border: 0;
}

.terms-of-use-wrap {
  display: flex;
  gap: 130px;
}
.terms-of-use-wrap > .left {
  width: 360px;
  flex-shrink: 0;
  height: max-content;
  top: 82px;
  border-left: 1px solid var(--line);
}
.terms-of-use-wrap > .left h6 {
  position: relative;
  padding: 10px 0 10px 16px;
  cursor: pointer;
}
.terms-of-use-wrap > .left h6::before {
  position: absolute;
  content: "";
  top: 0;
  left: -1px;
  width: 2px;
  height: 0;
  background-color: var(--main);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.terms-of-use-wrap > .left h6.active::before {
  height: 100%;
}
.terms-of-use-wrap > .right {
  flex-grow: 1;
}
.terms-of-use-wrap > .right .heading {
  margin-bottom: 40px;
}
.terms-of-use-wrap > .right .terms-of-use-item:not(:last-child) {
  margin-bottom: 32px;
}
.terms-of-use-wrap > .right .terms-of-use-item .terms-of-use-title {
  margin-bottom: 12px;
}
.terms-of-use-wrap > .right .terms-of-use-item .terms-of-use-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.login-wrap {
  display: flex;
  gap: 120px;
  align-items: center;
  position: relative;
}
.login-wrap .left {
  width: 100%;
}
.login-wrap .left .heading {
  margin-bottom: 28px;
}
.login-wrap .right {
  width: 100%;
}
.login-wrap .right p {
  margin-bottom: 28px;
}
.login-wrap .right a {
  padding: 10px 32px;
}
.login-wrap::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  background-color: var(--line);
  top: 0;
  left: 50%;
}

.my-account-wrap {
  display: flex;
  gap: 81px;
  max-width: 1130px;
  margin-left: auto;
  margin-right: auto;
}
.my-account-wrap .my-account-content {
  width: 100%;
}
.my-account-wrap .wrap-sidebar-account {
  width: 369px;
  flex-shrink: 0;
}

.sidebar-account {
  background-color: var(--surface);
  border-radius: 20px;
  padding: 40px 32px;
}
.sidebar-account .account-avatar {
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.sidebar-account .account-avatar .image {
  margin-bottom: 16px;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  overflow: hidden;
  margin-left: auto;
  margin-right: auto;
}
.sidebar-account .account-avatar .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.sidebar-account .my-account-nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.sidebar-account .my-account-nav .my-account-nav-item {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 16px 20px;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  border-radius: 16px;
}
.sidebar-account .my-account-nav .my-account-nav-item:hover, .sidebar-account .my-account-nav .my-account-nav-item.active {
  background-color: var(--white);
}

.btn-sidebar-account {
  position: fixed;
  top: 200px;
  left: 0;
  z-index: 50;
}
.btn-sidebar-account button {
  width: 40px;
  height: 40px;
  border-radius: 0;
  border: 1px solid var(--main);
  padding: 0;
  justify-content: center;
}
.btn-sidebar-account button:hover {
  background-color: var(--white);
}

.account-details .form-account-details {
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.account-details .title {
  margin-bottom: 16px;
}
.account-details select {
  border-radius: 8px;
  padding: 12px 16px;
}

.account-orders .wrap-account-order {
  overflow-x: auto;
}
.account-orders .wrap-account-order::-webkit-scrollbar {
  height: 3px;
}
.account-orders .wrap-account-order::-webkit-scrollbar-thumb {
  background: var(--surface);
  border-radius: 999px;
}
.account-orders table {
  width: 100%;
  min-width: 700px;
}
.account-orders table thead,
.account-orders table td,
.account-orders table th {
  padding: 15px 20px;
}
.account-orders table thead tr {
  border: 1px solid var(--line);
  background-color: var(--surface);
}
.account-orders table tbody {
  border: 1px solid var(--line);
  border-top: 0;
}
.account-orders table tbody tr:not(:last-child) {
  border-bottom: 1px solid var(--line);
}

.badge {
  padding: 5px 10px;
  font-weight: 500;
  background-color: var(--primary);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  min-width: 22px;
  min-height: 22px;
  text-transform: uppercase;
  text-align: center;
}

.widget-timeline .timeline {
  list-style: none;
  position: relative;
}
.widget-timeline .timeline::before {
  top: 20px;
  bottom: 48px;
  position: absolute;
  content: " ";
  width: 2px;
  left: 10px;
  border-right: 1px dashed var(--secondary-2);
}
.widget-timeline .timeline > li {
  margin-bottom: 15px;
  position: relative;
}
.widget-timeline .timeline > li .timeline-box {
  padding: 10px 10px 10px 15px;
  position: relative;
  display: block;
  margin-left: 40px;
}
.widget-timeline .timeline > li .timeline-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  height: 22px;
  left: 0;
  position: absolute;
  top: 10px;
  width: 22px;
  padding: 4px;
  background-color: var(--white);
  border: 1.5px solid var(--secondary-2);
}
.widget-timeline .timeline > li .timeline-badge::after {
  content: "";
  width: 10px;
  height: 10px;
  border-radius: 100%;
  display: block;
  background: var(--secondary-2);
}
.widget-timeline .timeline > li .timeline-badge.success {
  border-color: var(--success);
}
.widget-timeline .timeline > li .timeline-badge.success::after {
  background: var(--success);
}

.wd-form-order {
  padding: 15px;
  border-radius: 10px;
  border: 1px solid var(--line);
}
.wd-form-order .order-head {
  display: flex;
  align-items: center;
  border-bottom: 1px dashed var(--line);
  padding-bottom: 20px;
  margin-bottom: 30px;
  gap: 12px;
}
.wd-form-order .order-head .img-product {
  width: 80px;
  height: 80px;
  border: 1px solid var(--line);
  border-radius: 3px;
}
.wd-form-order .order-head .img-product img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.wd-form-order .widget-order-tab {
  margin-top: 30px;
}

.show-form-address,
.edit-form-address {
  display: none;
}

.list-account-address {
  display: grid;
  gap: 40px 30px;
}

.section-newsletter {
  padding: 44px 15px;
  background-color: rgb(224, 198, 182);
}
.section-newsletter.type-space-2 {
  padding: 60px 15px;
}
.section-newsletter .content {
  width: 100%;
  max-width: 564px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}
.section-newsletter .content .heading {
  margin-bottom: 12px;
}
.section-newsletter .content .text {
  margin-bottom: 36px;
}
.section-newsletter form input {
  height: 60px;
  border-color: var(--line);
  padding-right: 172px;
}
.section-newsletter form button {
  width: max-content;
  top: 50%;
  transform: translateY(-50%);
  right: 4px;
  border-radius: 99px;
  padding: 12px 40px;
  height: 44px;
  font-size: 14px;
}

.wg-free-delivery {
  border-radius: 12px;
  overflow: hidden;
  background-color: #28513b;
}
.wg-free-delivery .free-delivery-img {
  width: 100%;
  height: 100%;
}
.wg-free-delivery .free-delivery-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.wg-free-delivery .free-delivery-info {
  padding: 60px 60px 60px 60px;
}
.wg-free-delivery .free-delivery-info .free-delivery-heading {
  margin-bottom: 12px;
}
.wg-free-delivery .free-delivery-info .tf-countdown,
.wg-free-delivery .free-delivery-info > .text {
  margin-bottom: 36px;
}
.wg-free-delivery .free-delivery-info .tf-countdown .js-countdown {
  padding-right: 17px;
}

.wg-library {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  background-color: #f1f2eb;
}
.wg-library .library-img {
  width: 65.9%;
}
.wg-library .library-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.wg-library .library-info {
  width: 34.1%;
  padding: 43px 48px;
}
.wg-library .library-info .library-heading {
  margin-bottom: 16px;
}
.wg-library .library-info > .text {
  margin-bottom: 32px;
}

.news-item {
  display: flex;
  gap: 24px;
  align-items: center;
}
.news-item .image {
  border-radius: 8px;
  width: 100%;
  max-width: 303px;
  overflow: hidden;
  height: 100%;
}
.news-item .content p {
  margin-bottom: 12px;
}
.news-item .content .title {
  margin-bottom: 8px;
}

.banner-supper-sale {
  padding: 21px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  background-color: rgba(228, 49, 49, 0.1);
}
.banner-supper-sale .code-sale {
  padding: 3.5px 10px;
  border-radius: 4px;
  font-size: 20px;
  font-weight: 700;
  line-height: 24.8px;
  color: var(--primary);
  border: 2px dashed var(--primary);
}
.banner-supper-sale a {
  padding: 6px 16px;
}

.grid-card-product {
  gap: 45px;
}
.grid-card-product .column-card-product {
  position: relative;
}
.grid-card-product .column-card-product > .heading {
  margin-bottom: 28px;
}
.grid-card-product .column-card-product .card-product:not(:last-child) {
  margin-bottom: 16px;
}

.wg-benefit {
  padding: 8px 0;
  background-color: rgb(253, 235, 235);
}
.wg-benefit .benefit-item {
  display: flex;
  gap: 8px;
  align-items: center;
}
.wg-benefit .benefit-item p {
  white-space: nowrap;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.section-pet-store {
  background-image: url(../images/section/bg-1.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.wg-pet-store {
  border-radius: 16px;
  overflow: hidden;
}
.wg-pet-store .pet-store-heading {
  padding: 10px 20px;
  display: flex;
  gap: 16px;
  align-items: center;
}
.wg-pet-store .pet-store-heading .image {
  width: 58px;
  height: 58px;
  border-radius: 50%;
  overflow: hidden;
}
.wg-pet-store .pet-store-heading .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.wg-pet-store .pet-store-list {
  padding: 8px 0;
  background-color: var(--white);
}
.wg-pet-store .pet-store-list .pet-store-item {
  display: flex;
  padding: 11px 24px;
}

.wg-big-save {
  position: relative;
}
.wg-big-save > img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
  min-height: 320px;
}
.wg-big-save .content {
  position: absolute;
  top: 50%;
  left: 60px;
  transform: translateY(-50%);
  z-index: 5;
}
.wg-big-save .content .heading {
  margin-bottom: 12px;
}
.wg-big-save .content > .text {
  margin-bottom: 26px;
}

.section-flash-sale {
  background-image: url(../images/section/bg-2.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.section-flash-sale .wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 482px;
}
.section-flash-sale .wrap > img {
  margin-top: -130px;
}
.section-flash-sale .wrap .left {
  min-width: 220px;
}
.section-flash-sale .wrap .left h1 {
  margin-bottom: 12px;
}
.section-flash-sale .wrap .left p {
  margin-bottom: 30px;
}
.section-flash-sale .wrap .left ul li:not(:last-child) {
  margin-bottom: 8px;
}
.section-flash-sale .wrap .right h3 {
  margin-bottom: 8px;
}
.section-flash-sale .wrap .right p {
  margin-bottom: 28px;
}
.section-flash-sale .wrap .right .tf-countdown-lg {
  margin-bottom: 28px;
}
.section-flash-sale .wrap .right .tf-countdown-lg .countdown__timer {
  justify-content: center;
}
.section-flash-sale .wrap .right .tf-countdown-lg .countdown__item {
  background-color: var(--white);
  border-radius: 8px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loadmore-item .fl-item {
  display: none;
}

.page-search-inner .tf-col-quicklink {
  display: flex;
  flex-wrap: wrap;
  margin-top: 14px;
}
.page-search-inner .tf-col-quicklink .title {
  font-weight: 600;
  margin-right: 9px;
}

/* sib-form */
.sib-form {
  padding: 0;
  font-family: "Kumbh Sans", sans-serif !important;
}

#sib-container {
  background-color: unset;
  padding: 0;
  display: block;
}

.sib-form .entry__field {
  background-color: unset;
  border: 0;
  box-shadow: none !important;
  margin: 0;
}

.sib-form .sib-form-container .input {
  border: 2px solid var(--line);
  outline: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  font-size: 16px;
  line-height: 26px;
  border-radius: 8px;
  padding: 9px 16px;
  width: 100%;
  background: var(--white);
  color: var(--main);
  font-weight: 400;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  box-sizing: border-box !important;
}
.sib-form .sib-form-container .input:hover, .sib-form .sib-form-container .input:focus {
  border-color: var(--main);
}
.sib-form .sib-form-container .input::placeholder {
  color: var(--secondary-2);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.sib-form .entry__specification,
.sib-form .entry__error,
.entry__specification,
.entry__label {
  margin: 0 !important;
}

.sib-form-block {
  padding: 0;
}

.sib-optin {
  display: none;
}

#sib-form {
  position: relative;
}

#error-message {
  color: var(--critical);
}

#success-message {
  color: var(--success);
}

.sib-form-message-panel {
  border: 0;
  padding: 0;
}
.sib-form-message-panel .sib-form-message-panel__text {
  justify-content: center;
  padding: 0;
}

.sib-form-block__button .clickable__icon {
  display: none;
}

.banner-text {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}
.banner-text .box-title {
  display: grid;
  gap: 8px;
}
.banner-text.style-2 {
  padding: 30px;
}

.image-100 {
  width: 100%;
  height: 100%;
}
.image-100 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-item-2 {
  display: flex;
  gap: 20px;
  border-radius: 16px 0px 0px 16px;
  overflow: hidden;
}
.news-item-2 .image {
  overflow: hidden;
  max-width: 320px;
  width: 100%;
}
.news-item-2 .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.news-item-2 .content {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
}
.news-item-2 .meta-list {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}
.news-item-2 .meta-list li {
  display: flex;
  align-items: center;
  gap: 8px;
}
.news-item-2 .title {
  margin-bottom: 8px;
}

.cd-category {
  background-color: rgba(228, 49, 49, 0.1019607843);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  max-width: 236px;
  width: 100%;
  display: flex;
  justify-content: center;
}
.cd-category .js-countdown {
  padding: 12px 28px;
}
.cd-category .js-countdown .countdown__item {
  color: var(--critical);
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
}

.s-testimonial {
  gap: 30px;
  padding: 30px 15px;
  border-radius: 18px 18px 0px 0px;
  border-bottom: 1px solid var(--line);
  background-color: var(--white);
}
.s-testimonial .content-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  margin-bottom: 50px;
}

.tf-page-cart-checkout {
  padding: 25px;
  background-color: var(--surface);
  border-radius: 2.5px;
}
.tf-page-cart-checkout .shipping-calculator {
  padding-bottom: 18px;
  border-bottom: 1px solid var(--line);
}
.tf-page-cart-checkout .shipping-calculator .shipping-calculator-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 19.2px;
}
.tf-page-cart-checkout .shipping-calculator .shipping-calculator_accordion-icon {
  width: 20px;
  height: 20px;
  flex: 0 0 auto;
  margin-inline-start: 5px;
  position: relative;
}
.tf-page-cart-checkout .shipping-calculator .shipping-calculator_accordion-icon::after, .tf-page-cart-checkout .shipping-calculator .shipping-calculator_accordion-icon::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);
  background-color: currentColor;
  transition: transform 0.35s ease-in-out, opacity 0.35s ease-in-out;
  width: 12px;
  height: 2px;
  opacity: 1;
  border-radius: 9999px;
}
.tf-page-cart-checkout .shipping-calculator .shipping-calculator_accordion-icon::after {
  height: 12px;
  width: 2px;
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-header:not(.collapsed) .shipping-calculator_accordion-icon::after, .tf-page-cart-checkout .shipping-calculator .accordion-shipping-header:not(.collapsed) .shipping-calculator_accordion-icon::before {
  transform: translate(-50%, -50%) rotate(90deg);
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-header:not(.collapsed) .shipping-calculator_accordion-icon::before {
  opacity: 0;
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content {
  margin-top: 20px;
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content .tf-select {
  height: 50px;
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content .field {
  margin-bottom: 15px;
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content .field .label {
  margin-bottom: 8px;
  font-weight: 400;
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content .field .tf-select, .tf-page-cart-checkout .shipping-calculator .accordion-shipping-content .field input {
  border-color: transparent;
}
.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content .tf-btn {
  font-weight: 600;
  min-width: 199px;
}
.tf-page-cart-checkout .cart-checkbox {
  margin-top: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}
.tf-page-cart-checkout .cart-checkbox .tf-check {
  width: 16px;
  height: 16px;
  border-color: var(--line);
  background-color: var(--white);
  border-radius: 999px;
  overflow: hidden;
}
.tf-page-cart-checkout .cart-checkbox .tf-check:checked {
  border-color: var(--primary);
  background-color: var(--primary);
}
.tf-page-cart-checkout .cart-checkbox .tf-check::before {
  font-size: 6px;
}
.tf-page-cart-checkout .cart-checkbox a {
  text-decoration: underline;
  text-underline-offset: 2px;
  font-weight: 600;
}
.tf-page-cart-checkout .tf-cart-totals-discounts {
  margin-top: 18px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tf-page-cart-checkout .tf-cart-totals-discounts h3 {
  font-size: 20px;
  line-height: 24px;
}
.tf-page-cart-checkout .tf-cart-totals-discounts .total-value {
  font-size: 20px;
  line-height: 24px;
  font-weight: 600;
}
.tf-page-cart-checkout .tf-cart-tax {
  margin-top: 18px;
  color: var(--text);
}
.tf-page-cart-checkout .tf-cart-tax a {
  color: rgba(0, 0, 0, 0.85);
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  text-decoration-line: underline;
  transition: text-decoration-thickness 1s ease;
}
.tf-page-cart-checkout .tf-cart-tax a:hover {
  color: var(--main);
  text-decoration-thickness: 2px;
}
.tf-page-cart-checkout .cart-checkout-btn {
  margin-top: 18px;
}
.tf-page-cart-checkout .cart-checkout-btn .tf-btn {
  font-weight: 400;
}
.tf-page-cart-checkout .tf-page-cart_imgtrust {
  margin-top: 18px;
}
.tf-page-cart-checkout .tf-page-cart_imgtrust p {
  margin-bottom: 10px;
}
.tf-page-cart-checkout .tf-page-cart_imgtrust .cart-list-social {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/*-------------- Responsive ----------------- */
/* Media Queries
-------------------------------------------------------------- */
@media (min-width: 576px) {
  .collection-default.abs-x-center.abs-top .content {
    top: 42px;
  }
  .tab-product.style-2 {
    gap: 12px;
  }
  .tab-product.style-2 .nav-tab-item a {
    font-size: 16px;
    line-height: 24px;
  }
  .tf-sw-testimonial .box-navigation.style-2 {
    margin-top: 0;
    position: absolute;
    bottom: 10px;
    right: 0px;
    z-index: 3;
  }
  .tf-grid-layout.sm-col-2 {
    grid-template-columns: 1fr 1fr;
  }
  .tf-grid-layout.sm-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .tf-grid-layout.sm-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  .tf-grid-layout.sm-col-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  .tf-grid-layout.sm-col-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  .tf-grid-layout.sm-col-7 {
    grid-template-columns: repeat(7, 1fr);
  }
  .gallery-item .box-icon {
    width: 40px;
    height: 40px;
    font-size: 24px;
  }
  .flat-img-with-text-v4 .relatest-post .relatest-post-item .image {
    width: 180px;
    height: 120px;
  }
  .flat-img-with-text-v4 .relatest-post .relatest-post-item .content {
    gap: 12px;
  }
}
@media (min-width: 768px) {
  .text-clamp-md-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }
  .s-testimonial {
    border-bottom: 1px solid var(--line);
    padding: 30px;
  }
  .s-testimonial .content-left {
    max-width: 340px;
    border-right: 1px solid var(--line);
    padding-right: 30px;
  }
  .h-md-100 {
    height: 100% !important;
  }
  .canvas-filter {
    max-width: 400px;
  }
  .canvas-filter .canvas-header {
    padding: 15px 32px;
  }
  .canvas-filter .canvas-body {
    padding: 20px 32px;
  }
  .canvas-filter .canvas-bottom {
    padding: 18px 32px;
  }
  .widget-facet {
    padding-bottom: 32px;
  }
  .widget-facet.facet-price .box-price-product {
    gap: 20px;
  }
  .list-account-address {
    grid-template-columns: repeat(2, 1fr);
  }
  .about-us-features {
    max-width: 620px;
  }
  .about-us-content {
    max-width: 610px;
    margin-left: auto;
  }
  .frequently-bought-together-2,
  .slider-scroll,
  .thumbs-slider {
    max-width: 615px;
  }
  .tf-product-info-list {
    max-width: 615px;
    margin-left: auto;
  }
  .canvas-mb {
    width: 100% !important;
    max-width: 367px;
  }
  .canvas-mb .mb-canvas-content {
    min-width: 367px;
  }
  .tf-grid-layout.md-col-2 {
    grid-template-columns: 1fr 1fr;
  }
  .tf-grid-layout.md-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .tf-grid-layout.md-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  .tf-grid-layout.md-col-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  .tf-grid-layout.md-col-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  .tf-grid-layout.md-col-7 {
    grid-template-columns: repeat(7, 1fr);
  }
  .card-product .card-product-wrapper .on-sale-wrap {
    top: 12px;
    right: 12px;
    left: 12px;
  }
  .card-product.card-product-size .list-btn-main {
    bottom: 44px;
  }
  .card-product.style-7.card-product-size .list-product-btn, .card-product.style-6.card-product-size .list-product-btn, .card-product.style-2.card-product-size .list-product-btn {
    bottom: 44px;
  }
  .flat-img-with-text {
    flex-direction: row;
  }
  .flat-img-with-text .banner-right {
    padding-top: 40px;
  }
  .flat-img-with-text .banner-left {
    padding-bottom: 40px;
    padding-right: 60px;
  }
  .flat-img-with-text .banner-content {
    position: absolute;
    bottom: 40px;
    left: 25.3%;
    transform: unset;
    top: auto;
  }
  .flat-img-with-text-v2 .banner-left {
    margin-bottom: 0;
  }
  .collection-position-2 .on-sale-wrap {
    top: 12px;
    right: 12px;
    left: 12px;
  }
  .grid-cls-v1 {
    display: grid;
    grid-template-areas: "item1 item2 item4" "item1 item3 item4";
    gap: 15px;
    grid-template-columns: 1fr 1fr 1fr;
  }
  .grid-cls-v1 .item1 {
    grid-area: item1;
  }
  .grid-cls-v1 .item2 {
    grid-area: item2;
  }
  .grid-cls-v1 .item3 {
    grid-area: item3;
  }
  .grid-cls-v1 .item4 {
    grid-area: item4;
  }
  .grid-cls-v1 .collection-position-2 .img-style {
    height: 100%;
  }
  .grid-cls-v2 {
    display: grid;
    grid-template-areas: "item1 item2" "item1 item3";
    gap: 15px;
  }
  .grid-cls-v2 .item1 {
    grid-area: item1;
  }
  .grid-cls-v2 .item2 {
    grid-area: item2;
  }
  .grid-cls-v2 .item3 {
    grid-area: item3;
  }
  .grid-cls-4 {
    grid-template-areas: "item1 item2" "item3 item3";
    grid-template-columns: 1fr 1fr;
  }
  .tf-grid-layout .banner-discover-left {
    padding-right: 15px;
  }
  .tf-grid-layout .banner-discover-right {
    padding-left: 15px;
  }
  .flat-banner-parallax-v3 .fl-content,
  .flat-banner-parallax-v2 .fl-content {
    padding: 30px;
  }
  .tf-compare-col {
    min-width: 300px;
  }
}
@media (min-width: 992px) {
  .news-item-2 {
    gap: 40px;
  }
  .grid-cls-v3 {
    gap: 30px;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-areas: "aa aa cc" "bb bb cc";
  }
  .grid-cls-v3 .item1 {
    grid-area: aa;
  }
  .grid-cls-v3 .item2 {
    grid-area: bb;
  }
  .grid-cls-v3 .item3 {
    grid-area: cc;
  }
  .row-gap-lg-40 {
    row-gap: 40px !important;
  }
  .slider-parallax .wrap-slider {
    background-attachment: fixed;
  }
  #scroll-top {
    bottom: 40px;
    right: 40px;
  }
  #scroll-top.type-1 {
    bottom: 113px;
  }
  .canvas-filter .canvas-bottom .tf-btn {
    padding: 12px;
  }
  .image-select.type-currencies > .dropdown-menu, .image-select.type-languages > .dropdown-menu {
    margin-left: -20px !important;
  }
  .modal.fullRight.fade {
    transition: opacity 0.3s linear;
  }
  .page-title {
    padding: 68px 0 90px;
  }
  .breadcrumbs-default {
    padding: 60px 0px 80px;
  }
  .btn-sidebar-account {
    display: none;
  }
  .tab-shipping.type-two-cols {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
  .sw-dots {
    gap: 12px;
  }
  .sw-dots:not(.swiper-pagination-lock) {
    margin-top: 40px;
  }
  .nav-sw {
    width: 44px;
    height: 44px;
  }
  .nav-sw .icon {
    font-size: 20px;
  }
  .nav-sw.space-md {
    height: 60px;
    width: 60px;
  }
  .nav-sw.space-md.nav-sw-left {
    left: 40px;
  }
  .nav-sw.space-md.nav-sw-right {
    right: 40px;
  }
  .nav-sw.space-md .icon {
    font-size: 30px;
  }
  .slider-default.slider-position .box-content {
    left: 60px;
    right: 60px;
    bottom: 60px;
  }
  .tf-slideshow.slider-electronic .wrap-slider {
    height: 600px;
  }
  .tf-slideshow .card-box {
    padding: 30px;
  }
  .wrap-slider .content-slider {
    gap: 40px;
  }
  .wrap-slider .box-title-slider {
    gap: 16px;
  }
  .wrap-slider .box-content.type-2 .content-slider {
    gap: 36px;
  }
  .wrap-slider .box-content.type-2 .content-slider.gap-lg-40 {
    gap: 40px;
  }
  .wrap-slider .box-content.type-3 .content-slider {
    gap: 32px;
  }
  .wrap-slider .box-content.type-3 .tag {
    margin-bottom: -4px;
  }
  .sidebar.maxw-360 {
    max-width: 360px;
    margin-left: auto;
  }
  .tf-topbar .topbar-left {
    gap: 20px;
  }
  .tf-grid-layout {
    column-gap: 30px;
    row-gap: 30px;
  }
  .tf-grid-layout .column-xl-gap-20 {
    gap: 20px;
  }
  .tf-grid-layout.column-gap-xl-28 {
    column-gap: 28px;
  }
  .tf-grid-layout.row-gap-xl-16 {
    row-gap: 16px;
  }
  .tf-grid-layout.lg-col-2 {
    grid-template-columns: 1fr 1fr;
  }
  .tf-grid-layout.lg-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .tf-grid-layout.lg-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  .tf-grid-layout.lg-col-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  .tf-grid-layout.lg-col-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  .tf-grid-layout.lg-col-7 {
    grid-template-columns: repeat(7, 1fr);
  }
  .card-product .card-product-info {
    padding-top: 16px;
  }
  .card-product .list-btn-main {
    bottom: 12px;
    left: 14px;
    right: 14px;
  }
  .card-product .list-btn-main .box-icon {
    width: 42px;
    height: 42px;
  }
  .card-product .list-btn-main .box-icon .icon {
    font-size: 24px;
  }
  .card-product .btn-main-product {
    padding: 10px;
  }
  .card-product .list-product-btn {
    top: 8px;
    right: 8px;
    gap: 8px;
  }
  .card-product .list-product-btn .box-icon {
    width: 40px;
    height: 40px;
    min-width: 40px;
  }
  .card-product .list-product-btn .box-icon .icon {
    font-size: 24px;
  }
  .card-product.style-7 .list-product-btn, .card-product.style-6 .list-product-btn, .card-product.style-2 .list-product-btn {
    bottom: 20px;
  }
  .card-product.style-5 .list-btn-main {
    gap: 12px;
  }
  .card-product.style-6 .list-product-btn {
    gap: 12px;
  }
  .card-product.style-swatch-img .list-color-product {
    gap: 4px;
  }
  .card-product.style-swatch-img .list-color-product .list-color-item {
    width: 40px;
    height: 40px;
  }
  .card-product .box-icon svg {
    width: 24px;
  }
  .card-product.style-list {
    gap: 40px;
  }
  .card-product.style-list .list-product-btn {
    gap: 12px;
  }
  .card-product.style-list .list-product-btn .btn-main-product {
    max-width: 272px;
    width: 100%;
    border: 2px solid var(--line);
    height: 48px;
    line-height: 48px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .card-product.style-list .list-product-btn .box-icon {
    border: 2px solid var(--line);
    width: 48px;
    height: 48px;
    min-width: 48px;
  }
  .card-product.style-list .size-box {
    margin-bottom: 20px;
  }
  .card-product.style-list .size-box .size-item {
    font-size: 16px;
    line-height: 24px;
  }
  .card-product.style-list .list-color-product {
    margin-bottom: 12px;
  }
  .card-product.style-list .title {
    font-size: 20px;
    line-height: 30px;
  }
  .card-product.list-st-2 {
    gap: 24px;
  }
  .card-product.list-st-2 .card-product-info {
    gap: 16px;
    padding: 12px 0px;
  }
  .card-product.list-st-2 .box-icon {
    width: 48px;
    height: 48px;
    min-width: 48px;
  }
  .card-product.list-st-3 {
    border-width: 2px;
    padding: 24px;
    gap: 32px;
  }
  .card-product.list-st-3 .inner-wrapper-card .box-progress-stock {
    margin-top: 16px;
  }
  .card-product.list-st-3 .card-product-info {
    gap: 20px;
  }
  .card-product.list-st-3 .card-product-info .inner-top {
    gap: 8px;
    padding-bottom: 12px;
    margin-bottom: 12px;
  }
  .card-product.list-st-3 .card-product-info .title,
  .card-product.list-st-3 .card-product-info .price {
    font-size: 20px;
    line-height: 28px;
  }
  .card-product.list-st-3 .card-product-info .inner-bottom {
    gap: 16px;
  }
  .card-product.list-st-3 .card-product-info .list-product-btn {
    gap: 10px;
  }
  .card-product.list-st-3 .card-product-info .list-product-btn .box-icon {
    border-width: 2px;
    width: 40px;
    height: 40px;
  }
  .card-product.list-st-3 .card-product-info .list-product-btn .box-icon .tooltip {
    margin: 0;
  }
  .card-product.list-st-3 .card-product-info .list-product-btn .btn-main-product {
    height: 40px;
    line-height: 40px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .card-product .countdown-box .countdown__timer {
    gap: 18px;
  }
  .collection-default {
    gap: 32px;
  }
  .collection-default .content {
    gap: 12px;
  }
  .collection-default.abs-top-2 .content {
    top: 70px !important;
  }
  .collection-position.style-lg .content {
    gap: 12px;
  }
  .collection-position-2 .content {
    left: 20px;
    right: 20px;
    bottom: 20px;
  }
  .collection-position-2 .cls-btn .icon {
    font-size: 20px;
  }
  .collection-position-2.style-2 .cls-btn {
    padding: 16px 28px;
  }
  .collection-position-2.style-3 .cls-btn {
    padding: 12px 24px;
  }
  .collection-position-2.style-4 .cls-btn {
    padding: 12px 52px;
  }
  .collection-position-2.style-5 .content {
    bottom: 40px;
  }
  .collection-position-2.style-5.has-overlay .content {
    bottom: 20px;
  }
  .collection-position-2.style-6 .cls-btn {
    padding: 12px 40px;
  }
  .collection-position-2.style-7 .content {
    left: 28px;
    right: 28px;
    bottom: 28px;
  }
  .collection-position-2.style-7 .content.space-lg {
    top: 48px;
  }
  .collection-position-2.style-8 .top {
    top: 24px;
  }
  .collection-position-2 .cls-content {
    padding: 16px 20px;
  }
  .collection-position-2.radius-lg {
    border-radius: 40px;
  }
  .collection-position-3 .archive-top {
    left: 40px;
    top: 22px;
    gap: 12px;
  }
  .collection-position-3 .archive-btn {
    left: 40px;
    bottom: 40px;
  }
  .slider-collection .collection-position-2 .cls-btn {
    padding: 12px 52px;
  }
  .slider-collection .collection-position-2 .cls-btn .icon {
    font-size: 24px;
  }
  .slider-collection .collection-position-2 .content {
    bottom: 40px;
  }
  .tab-product {
    gap: 40px;
    margin-bottom: 40px;
  }
  .tab-product .nav-tab-item a {
    font-size: 30px;
    line-height: 42px;
  }
  .swiper .sec-btn,
  .flat-animate-tab .sec-btn {
    margin-top: 40px;
  }
  .flat-countdown-banner .tf-countdown-lg .countdown__timer {
    display: flex;
    justify-content: flex-end;
  }
  .flat-countdown-banner .banner-img {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    right: auto;
  }
  .testimonial-item .img-style .box-icon {
    visibility: hidden;
    opacity: 0;
    width: 40px;
    height: 40px;
    font-size: 24px;
  }
  .testimonial-item:hover .img-style .box-icon {
    visibility: visible;
    opacity: 1;
  }
  .tf-marquee .marquee-child-item {
    padding-left: 25px;
    padding-right: 25px;
  }
  .tf-marquee.marquee-style2 .marquee-child-item {
    padding-left: 20px;
    padding-right: 20px;
  }
  .tf-marquee.marquee-style3 .marquee-child-item {
    padding-left: 60px;
    padding-right: 60px;
  }
  .loobook-product {
    gap: 12px;
  }
  .loobook-product .content .btn-lookbook {
    margin-top: 12px;
  }
  .loobook-product.style-row .img-style {
    max-width: 100px;
    max-height: 100px;
  }
  .lookbook-item .dropend .dropdown-menu {
    --bs-dropdown-min-width: 16rem;
  }
  .flat-img-with-text {
    display: flex;
    justify-content: space-between;
    gap: 30px;
  }
  .flat-img-with-text .banner {
    width: 50%;
  }
  .flat-img-with-text .banner-right {
    padding-top: 60px;
  }
  .flat-img-with-text .banner-left {
    padding-bottom: 60px;
    padding-right: 80px;
  }
  .flat-img-with-text .banner-content {
    padding: 40px 60px 37px 60px;
    bottom: 60px;
  }
  .flat-img-with-text .banner-content .content-text {
    margin-bottom: 32px;
    gap: 12px;
  }
  .tab-banner .nav-tab-item .nav-tab-link {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .tab-banner .nav-tab-item .nav-tab-link .icon {
    font-size: 20px;
  }
  .testimonial-item-v2 {
    gap: 20px;
  }
  .testimonial-item-v2 .quote-box {
    gap: 20px;
  }
  .testimonial-item-v2 .quote-box .icon {
    font-size: 44px;
  }
  .tf-sw-testimonial .box-navigation {
    margin-top: 28px;
  }
  .flat-img-with-text-v3 .banner-left {
    padding-right: 60px;
  }
  .flat-img-with-text-v3 .box-title {
    margin-bottom: 40px;
    gap: 12px;
  }
  .flat-img-with-text-v4 {
    gap: 60px;
    grid-template-columns: 1fr 1fr;
  }
  .list-collection {
    gap: 40px;
    margin-bottom: 40px;
  }
  .list-collection.style-lg .cls-item {
    gap: 40px;
  }
  .list-collection.style-lg .cls-item .img-cls {
    width: 133px;
    height: 100px;
  }
  .flat-countdown-banner-2 .box-content .tf-countdown-lg {
    margin-top: 24px;
  }
  .flat-countdown-banner-2 .box-content .btn-banner {
    margin-top: 40px;
  }
  .banner-cls {
    position: relative;
  }
  .banner-cls .cls-content {
    gap: 36px;
    left: 40px;
    right: 40px;
    bottom: 40px;
  }
  .banner-cls .cls-content .box-title-cls {
    gap: 12px;
  }
  .flat-single-home:not(.flat-single-home-default) .tf-product-info-wrap,
  .flat-single-home:not(.flat-single-home-default) .tf-product-media-wrap {
    padding-left: 15px;
    padding-right: 15px;
  }
  .flat-single-home.flat-single-home-default .tf-product-info-wrap {
    padding-left: 15px;
  }
  .flat-banner-parallax {
    padding: 182px 0px;
  }
  .flat-banner-parallax .fl-content {
    gap: 36px;
  }
  .flat-banner-parallax-v3 .fl-content,
  .flat-banner-parallax-v2 .fl-content {
    padding: 40px;
  }
  .grid-cls-v2 {
    gap: 30px;
  }
  .testimonial-item.style-3 {
    padding: 32px;
  }
  .banner-cls-discover .cls-content {
    gap: 36px;
  }
  .banner-cls-discover .cls-content .box-title-top {
    gap: 12px;
  }
  .tf-grid-layout .banner-discover-left {
    padding-right: 30px;
  }
  .tf-grid-layout .banner-discover-right {
    padding-left: 30px;
  }
  .flat-with-text-lookbook-v2 {
    display: flex;
    align-items: center;
  }
  .flat-with-text-lookbook-v2 .lookbook-content {
    width: 50%;
    padding-bottom: 0;
  }
  .flat-with-text-lookbook-v2 .banner-img {
    width: 50%;
  }
  .wrapper-control-shop .tf-list-layout .card-product {
    margin-bottom: 40px;
    padding-bottom: 40px;
  }
  .wrapper-control-shop .tf-list-layout .wg-pagination {
    margin-top: 40px;
  }
  .wrapper-control-shop .tf-grid-layout {
    row-gap: 40px;
  }
  .tf-table-page-cart th {
    font-size: 20px;
    padding-left: 20px;
    padding-right: 20px;
    text-align: center;
  }
  .tf-table-page-cart th:last-child {
    padding-right: 0;
  }
  .tf-table-page-cart th:first-child {
    padding-left: 0;
    text-align: start;
  }
  .tf-table-page-cart td {
    padding: 28px 20px;
  }
  .box-order {
    padding: 24px;
  }
  .box-order .ship {
    gap: 40px;
  }
  .tf-page-checkout .wrap:not(:last-child) {
    margin-bottom: 40px;
  }
  .tf-page-checkout .wrap .title {
    margin-bottom: 20px;
  }
  .tf-page-checkout .login-box {
    padding: 20px;
  }
  .tf-page-checkout .login-box .grid-2 {
    gap: 20px;
  }
  .tf-page-checkout .info-box {
    gap: 20px;
  }
  .payment-box .payment-item:not(:last-child) {
    margin-bottom: 20px;
  }
  .payment-box .payment-item .payment-body {
    gap: 19px;
  }
  .payment-box .payment-item .payment-body .input-payment-box {
    gap: 20px;
  }
  .form-payment .tf-btn {
    margin-top: 40px;
  }
  .flat-sidebar-checkout .item-product {
    gap: 24px;
  }
}
@media (min-width: 1025px) {
  .flat-img-with-text .banner-content h3 {
    line-height: 52px;
  }
  .tf-marquee.marquee-style2 .marquee-child-item .icon {
    font-size: 40px;
  }
}
@media (min-width: 1200px) {
  .section-image-zoom.type-space-2 .thumbs-slider {
    max-width: 600px;
    gap: 12px;
  }
  .section-image-zoom.type-space-2 .thumbs-slider .tf-product-media-main {
    margin: 0;
  }
  .section-image-zoom.type-space-2 .tf-product-media-thumbs .item {
    max-width: 100px;
  }
  .section-image-zoom.type-space-2 .tf-product-info-list {
    max-width: 610px;
  }
  .section-newsletter.style-2 form input {
    height: 66px !important;
  }
  .section-newsletter.style-2 form button {
    height: 50px !important;
  }
  .testimonial-item-v2.type-space-2 {
    gap: 24px;
  }
  .tf-icon-box.style-2.type-2 .icon-box {
    width: 80px;
    height: 80px;
  }
  .wg-free-delivery.type-space-2 .free-delivery-info {
    padding-left: 80px;
  }
  .wg-free-delivery.type-space-2 .free-delivery-info .type-sale {
    margin-bottom: 12px;
  }
  .wg-free-delivery.type-space-2 .free-delivery-info > .text {
    margin-bottom: 24px;
  }
  .wg-free-delivery.type-space-2 .free-delivery-info .tf-countdown-lg {
    margin-bottom: 40px;
  }
  .wg-big-save.type-space-2 .content {
    left: 80px;
  }
  .wg-big-save.type-space-2 .type-sale {
    margin-bottom: 12px;
  }
  .wg-big-save.type-space-2 .heading {
    margin-bottom: 20px;
  }
  .wg-big-save.type-space-2 .sub-text {
    margin-bottom: 32px;
  }
  .mb-xl-12 {
    margin-bottom: 12px;
  }
  .news-item-2 .content {
    padding-right: 40px;
  }
  .flat-banner-parallax.style-2 {
    padding: 164px 0px;
  }
  .flat-banner-parallax.style-2 .fl-content {
    max-width: 538px;
    width: 100%;
    margin: auto;
  }
  .s-testimonial {
    padding: 60px;
    gap: 60px;
  }
  .s-testimonial .content-left {
    padding-right: 60px;
  }
  .testimonial-item-2 {
    gap: 35px;
  }
  .grid-cls-4 {
    gap: 40px;
  }
  .slider-icon-box {
    padding: 60px 14px 60px 60px;
  }
  .px_xl-40 {
    padding-left: 40px;
    padding-right: 40px;
  }
  .contact-wrap-form {
    padding: 40px;
    gap: 40px;
  }
  .contact-wrap-form .box-title {
    gap: 20px;
  }
  .contact-wrap-form.style-2 {
    padding: 40px 32px;
    gap: 60px;
  }
  .contact-wrap-form.style-2 #subscribe-content {
    gap: 12px !important;
  }
  .form-newsletter-subscribe-2 #subscribe-content {
    gap: 20px;
  }
  .news-item.style-2 .content {
    padding: 43px 40px;
  }
  .banner-text {
    gap: 40px;
  }
  .banner-text .box-title {
    gap: 16px;
  }
  .banner-text.style-2 {
    padding: 68px 100px 68px 60px;
  }
  .collection-default .tag {
    margin-bottom: -4px;
  }
  .collection-default.style-row .content {
    padding-left: 28px;
    padding-right: 25px;
  }
  .collection-default.style-row .desc {
    margin-bottom: 28px;
  }
  .collection-default.style-row.type-space-xl .content {
    padding-left: 48px;
  }
  .collection-default.wd-load .tag {
    margin-bottom: -8px;
  }
  .collection-default.wd-load .content {
    gap: 32px !important;
    left: 80px !important;
  }
  .collection-default.wd-load .content .box-title {
    gap: 20px;
  }
  .collection-default.abs-left-center .content {
    left: 40px;
  }
  .collection-default.abs-left-bottom .content {
    left: 40px;
    bottom: 40px;
  }
  .collection-default.abs-left-bottom.type-2 .content {
    left: 28px;
    bottom: 28px;
  }
  .collection-default.type-xl .content {
    gap: 20px;
  }
  .collection-default.type-xl .box-title {
    gap: 12px;
  }
  .collection-default.abs-x-center .content {
    gap: 24px;
    left: 28px;
    right: 28px;
  }
  .collection-default.abs-x-center .content .box-title {
    gap: 12px;
  }
  .collection-default.abs-x-center.abs-bottom .content {
    bottom: 28px;
  }
  .heading-section-3 {
    margin-bottom: 40px;
  }
  .heading-section-3 h2 {
    margin-bottom: 24px;
  }
  .lh-xl-20 {
    line-height: 20px;
  }
  .gap-xl-24 {
    gap: 24px !important;
  }
  .gap-xl-60 {
    gap: 60px;
  }
  .tf-topbar.topbar-fullwidth {
    padding-left: 60px;
    padding-right: 60px;
  }
  .tf-topbar.topbar-fullwidth-2 {
    padding-left: 40px;
    padding-right: 40px;
  }
  .tf-topbar.type-space-lg {
    padding-top: 6px;
    padding-bottom: 6px;
    padding-right: 40px;
  }
  .sidebar-filter .canvas-body {
    padding: 0;
  }
  .dropdown-filter {
    max-width: 100%;
    position: absolute;
    z-index: 100;
    padding: 30px 0px 40px;
    top: 100%;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden;
  }
  .dropdown-filter .widget-facet {
    margin-bottom: 0;
    padding-bottom: 0;
    border: 0;
  }
  .dropdown-filter .canvas-body {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 30px;
    row-gap: 40px;
  }
  .dropdown-filter.show {
    margin-top: 10px;
    opacity: 1;
    visibility: visible;
  }
  .container-full2 {
    padding: 0 40px;
  }
  .nav-sw.lg {
    width: 60px;
    height: 60px;
    border-color: transparent;
  }
  .nav-sw.lg .icon {
    font-size: 28px;
  }
  .slider-padding,
  .header-fullwidth {
    padding-left: 40px;
    padding-right: 40px;
  }
  .header-fullwidth-2 {
    padding-left: 60px;
    padding-right: 40px;
  }
  .slider-default .box-content {
    left: 80px;
    right: 80px;
    bottom: 80px;
  }
  .slider-default .box-content.type-2 {
    left: 60px;
    right: 60px;
    bottom: 60px;
  }
  .tf-slideshow .wrap-pagination {
    bottom: 40px;
  }
  .tf-slideshow .wrap-pagination.stype-space-3 {
    bottom: 28px;
  }
  .tf-slideshow .nav-sw.nav-sw-left {
    left: 60px;
  }
  .tf-slideshow .nav-sw.nav-sw-right {
    right: 60px;
  }
  .tf-slideshow.slider-position .wrap-pagination {
    bottom: 20px;
  }
  .tf-slideshow.slider-pos-nav .nav-sw.nav-sw-left {
    left: 40px;
  }
  .tf-slideshow.slider-pos-nav .nav-sw.nav-sw-right {
    right: 40px;
  }
  .tf-slideshow .card-box {
    padding: 60px;
  }
  .tf-slideshow .card-box.style-2 {
    margin-right: -10px;
  }
  .tf-slideshow.slideshow-effect .wrap-pagination {
    bottom: 32px;
  }
  .header-default .wrapper-header {
    min-height: 82px;
  }
  .header-absolute {
    margin-bottom: -82px;
  }
  .footer .footer-menu {
    gap: 100px;
  }
  .tf-dropdown-sort {
    padding: 5px 12px;
    min-width: 164px;
  }
  .tf-grid-layout.xl-col-2 {
    grid-template-columns: 1fr 1fr;
  }
  .tf-grid-layout.xl-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .tf-grid-layout.xl-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  .tf-grid-layout.xl-col-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  .tf-grid-layout.xl-col-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  .tf-grid-layout.xl-col-7 {
    grid-template-columns: repeat(7, 1fr);
  }
  .card-product .marquee-product {
    display: flex;
  }
  .card-product .tooltip,
  .card-product .countdown-wrap {
    display: block;
  }
  .card-product .list-product-btn {
    top: 12px;
    right: 12px;
  }
  .card-product:not(.style-list, .list-st-2, .list-st-3) .size-list,
  .card-product:not(.style-list, .list-st-2, .list-st-3) .list-btn-main {
    transform: translateY(100%);
    opacity: 0;
    visibility: hidden;
  }
  .card-product:not(.style-list, .list-st-2, .list-st-3) .box-icon {
    transform: translate(20px);
    opacity: 0;
    visibility: hidden;
  }
  .card-product:not(.style-list, .list-st-2, .list-st-3) .card-product-info .btn-main-product {
    transform: translateY(20px);
    opacity: 0;
    visibility: hidden;
  }
  .card-product:hover:not(.style-list, .list-st-2, .list-st-3) .marquee-product {
    opacity: 0;
    visibility: hidden;
  }
  .card-product:hover:not(.style-list, .list-st-2, .list-st-3) .variant-wrap {
    transform: translateY(100%);
    opacity: 0;
    visibility: hidden;
  }
  .card-product:hover:not(.style-list, .list-st-2, .list-st-3) .size-list,
  .card-product:hover:not(.style-list, .list-st-2, .list-st-3) .box-icon,
  .card-product:hover:not(.style-list, .list-st-2, .list-st-3) .list-btn-main {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translate(0) !important;
  }
  .card-product:hover:not(.style-list, .list-st-2, .list-st-3) .card-product-info .btn-main-product {
    transform: translate(0);
    opacity: 1;
    visibility: visible;
  }
  .card-product.style-7 .list-product-btn, .card-product.style-6 .list-product-btn, .card-product.style-2 .list-product-btn {
    left: 12px;
    right: 12px;
  }
  .card-product.style-7 .list-product-btn .box-icon, .card-product.style-6 .list-product-btn .box-icon, .card-product.style-2 .list-product-btn .box-icon {
    transform: translateY(20px);
  }
  .card-product.style-swatch-img .list-color-product {
    gap: 8px;
  }
  .card-product.style-swatch-img .list-color-product .list-color-item {
    width: 48px;
    height: 48px;
    padding: 4px;
    border: 2px solid transparent;
  }
  .card-product.list-st-3 .card-product-info {
    gap: 35px;
  }
  .tf-countdown-lg .countdown__timer {
    gap: 47px;
  }
  .tf-countdown-lg .countdown__item {
    min-width: 68px;
    min-height: 90px;
  }
  .tf-countdown-lg .countdown__item .countdown__value {
    font-size: 56px;
    line-height: 68px;
    margin-bottom: -3px;
  }
  .tf-countdown-lg .countdown__item:not(:last-child)::after {
    font-size: 30px;
    line-height: 42px;
    right: -27px;
  }
  .tf-countdown-lg.style-1 .countdown__timer {
    gap: 31px;
  }
  .tf-countdown-lg.style-1 .countdown__item {
    min-width: 88px;
    min-height: 88px;
    width: 80px;
    height: 80px;
  }
  .tf-countdown-lg.style-1 .countdown__item .countdown__value {
    font-size: 44px;
    font-weight: 500;
    line-height: 50px;
  }
  .tf-countdown-lg.style-1 .countdown__item:not(:last-child)::after {
    right: -20px;
  }
  .gallery-item .box-icon {
    opacity: 0;
    visibility: hidden;
  }
  .gallery-item:hover::before {
    opacity: 1;
    visibility: visible;
  }
  .gallery-item:hover .box-icon {
    opacity: 1;
    visibility: visible;
  }
  .lookbook-item .dropdown-menu {
    --bs-dropdown-min-width: 14rem;
  }
  .lookbook-item .dropend .dropdown-menu {
    --bs-dropdown-min-width: 324px;
  }
  .flat-img-with-text {
    display: flex;
    justify-content: space-between;
    gap: 30px;
  }
  .flat-img-with-text .banner {
    width: 50%;
  }
  .flat-img-with-text .banner-right {
    padding-top: 80px;
  }
  .flat-img-with-text .banner-left {
    padding-bottom: 80px;
    padding-right: 157px;
  }
  .flat-img-with-text .banner-content {
    bottom: 80px;
  }
  .flat-img-with-text-v2 {
    align-items: center;
  }
  .flat-img-with-text-v2 .banner-left {
    padding-right: 110px;
    gap: 60px;
  }
  .flat-img-with-text-v2 .banner-right {
    padding-left: 47px;
  }
  .flat-img-with-text-v3 .banner-left {
    padding-right: 158px;
  }
  .flat-img-with-text-v4 {
    gap: 110px;
  }
  .flat-with-text-lookbook .lookbook-content {
    padding-left: 110px;
    padding-right: 22px;
  }
  .flat-with-text-lookbook .lookbook-content .box-title {
    margin-bottom: 34px;
  }
  .flat-with-text-lookbook .lookbook-content .total-lb {
    margin-top: 34px;
  }
  .flat-with-text-lookbook-v2 .lookbook-content {
    padding: 30px 80px;
    padding-right: 100px;
  }
  .flat-with-text-lookbook-v2 .lookbook-content .box-title {
    margin-bottom: 32px;
  }
  .flat-with-text-lookbook-v2 .lookbook-content .total-lb {
    margin-top: 32px;
  }
  .tf-sw-testimonial .testimonial-item-v2 {
    padding-left: 140px;
    padding-right: 140px;
  }
  .grid-cls-v1 {
    gap: 30px;
  }
  .layout-sw-center {
    overflow: hidden;
  }
  .layout-sw-center .tf-sw-categories {
    margin: 0 -300px;
  }
  .flat-single-home:not(.flat-single-home-default) .tf-product-media-main {
    width: calc(100% - 110px);
  }
  .flat-single-home:not(.flat-single-home-default) .tf-product-media-main .item {
    max-height: 653px;
  }
  .flat-single-home:not(.flat-single-home-default) .tf-product-media-thumbs {
    width: 100px;
    max-height: 653px;
  }
  .tf-compare-field {
    padding: 15px 30px;
  }
  .tf-compare-item {
    padding: 32px 40px 20px;
  }
  .tf-compare-col {
    min-width: 350px;
  }
  .box-order .ship {
    gap: 65px;
  }
  .fl-sidebar-cart {
    padding-left: 30px;
  }
  .line-separation {
    width: 1px;
    height: 100%;
  }
  .nav-account .dropdown-account .list-menu-item a {
    padding: 16px 0px;
  }
  .nav-account .dropdown-login .tf-btn {
    padding: 12px 32px;
  }
  .wrapper-chat {
    right: 60px;
  }
  .hover-cursor-img {
    position: relative;
  }
  .hover-cursor-img .hover-image {
    position: fixed;
    transform: scale(0);
    pointer-events: none;
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    z-index: 1000;
    display: block;
  }
  .hover-cursor-img .hover-image img {
    max-width: 150px;
    box-shadow: var(--shadow1);
  }
}
@media (min-width: 1400px) {
  .slider-parallax .wrap-slider {
    min-height: 100vh;
  }
}
@media (min-width: 1440px) {
  .collection-default.abs-top-2 .content {
    top: 170px !important;
  }
  .collection-default.hover-button .content {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    margin-bottom: -60px;
  }
  .collection-default.hover-button .box-btn {
    opacity: 0;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    transform: translateY(20px);
  }
  .collection-default.hover-button:hover .content {
    margin-bottom: 0px;
  }
  .collection-default.hover-button:hover .box-btn {
    opacity: 1;
    transform: translateY(0px);
  }
  .mt--xxl-148 {
    margin-top: -148px;
  }
  .slider-default .box-content.type-3 {
    bottom: 80px;
    left: 80px;
    right: 80px;
  }
  .slider-default .box-content.type-3 .content-slider {
    gap: 40px;
  }
  .slider-video .wrap-slider {
    height: 680px;
  }
  .header-style-02 .logo-header {
    margin-left: 40px;
  }
  .header-default .box-support {
    margin-right: 44px;
  }
  .tf-grid-layout.xxl-col-2 {
    grid-template-columns: 1fr 1fr;
  }
  .tf-grid-layout.xxl-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .tf-grid-layout.xxl-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  .tf-grid-layout.xxl-col-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  .tf-grid-layout.xxl-col-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  .tf-grid-layout.xxl-col-7 {
    grid-template-columns: repeat(7, 1fr);
  }
  .tf-slideshow .slider-group {
    gap: 20px;
  }
  .tf-slideshow .slider-group img {
    border-radius: 8px;
  }
  .flat-collection-circle .nav-sw-left {
    margin-left: -22px;
  }
  .flat-collection-circle .nav-sw-right {
    margin-right: -22px;
  }
  .nav-account .dropdown-account {
    min-width: 250px;
    left: -125px;
  }
  .nav-account .dropdown-login {
    min-width: 290px;
  }
  .header-style-7 .nav-account .dropdown-login {
    left: -190px;
  }
  .header-fullwidth .nav-account .dropdown-login {
    left: auto;
    right: -100px;
  }
  .header-style-6 .nav-account .dropdown-login {
    left: -200px;
  }
}
@media (min-width: 1600px) {
  .nav-account .dropdown-account {
    left: -70px;
    min-width: 320px;
  }
  .nav-account .dropdown-login {
    left: -100px;
  }
  .header-style-6 .header-left {
    gap: 60px;
  }
}
@media only screen and (max-width: 1600px) {
  .header-style-6 .header-left {
    gap: 20px;
  }
  .header-style-6 .wrapper-header {
    padding: 0;
  }
}
@media only screen and (max-width: 1439px) {
  .tf-product-deals-item.style-column {
    padding: 5px 7px;
  }
  .header-style-6 .header-right {
    gap: 24px;
  }
  .header-style-6 .header-right .form-search::after {
    right: -12px;
  }
  .title-display-2 {
    font-size: 80px;
    line-height: 88px;
  }
  .my-account-wrap {
    gap: 40px;
  }
  .terms-of-use-wrap {
    gap: 60px;
  }
  .product-fixed-price .tf-product-info-list {
    padding: 32px 15px;
  }
  .tf-main-product.full-width {
    padding: 0 15px;
    gap: 0 30px;
  }
  .tf-main-product.full-width > div {
    width: calc(50% - 15px);
  }
  .offcanvas-compare .offcanvas-content .icon-close-popup {
    top: 10px;
    right: 10px;
    width: 25px;
    height: 25px;
    font-size: 10px;
  }
  .offcanvas-compare .offcanvas-content .tf-compare-list {
    gap: 20px;
  }
  .offcanvas-compare .offcanvas-content .tf-compare-list .tf-compare-wrap {
    padding-right: 0;
    margin-right: 0;
    gap: 25px;
  }
  .offcanvas-compare .offcanvas-content .tf-compare-list .tf-compare-item .icon-close {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }
  .offcanvas-compare .offcanvas-content .tf-compare-list .tf-compare-item > .btns-repeat {
    right: -22px;
  }
}
@media only screen and (max-width: 1399px) {
  .header-style-2 .box-nav-ul {
    gap: 13px;
  }
  .tf-slideshow .wrap-slider {
    height: 700px;
  }
  .wrapper-shop.tf-col-7 {
    column-gap: 15px;
  }
  .tf-control-layout .sw-layout-6,
  .tf-control-layout .sw-layout-7 {
    display: none;
  }
}
@media only screen and (max-width: 1199px) {
  .news-item-2 .image {
    width: 35%;
  }
  .news-item-2 .content {
    width: 60%;
  }
  .tf-topbar.type-1 {
    top: 59px;
  }
  .header-style-6 {
    background-color: var(--white);
  }
  .header-style-6 .wrapper-header {
    margin-top: 0;
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .collection-default.style-row .content {
    padding: 30px 15px;
  }
  .header-style-6 .wrapper-header {
    border-radius: 0;
  }
  .nav-account .dropdown-account {
    top: calc(100% + 20px);
  }
  .fl-sidebar-cart {
    margin-top: 30px;
  }
  .sidebar-filter {
    position: fixed;
    bottom: 0;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    background-clip: padding-box;
    outline: 0;
  }
  .sidebar-filter.left {
    top: 0;
    left: 0;
    transform: translateX(-100%);
  }
  .sidebar-filter.right {
    top: 0;
    right: 0;
    transform: translateX(100%);
  }
  .sidebar-filter.show {
    transform: none;
  }
  .wg-free-delivery .free-delivery-info {
    padding-left: 30px;
    padding-right: 30px;
  }
  .wg-library .library-info {
    padding-left: 15px;
    padding-right: 15px;
  }
  .wg-library .library-info .library-heading {
    margin-bottom: 12px;
  }
  .wg-library .library-info > .text {
    margin-bottom: 20px;
  }
  .tf-countdown.style-2 .countdown__timer {
    gap: 50px;
  }
  .tf-control-layout .sw-layout-5,
  .tf-control-layout .sw-layout-4 {
    display: none;
  }
  .login-wrap {
    gap: 60px;
  }
  .page-faqs-wrap {
    gap: 60px 30px;
  }
  .page-faqs-wrap .ask-question {
    width: 300px;
  }
  .page-404 .page-404-inner .content {
    max-width: unset;
  }
  .page-404 .page-404-inner .content .heading {
    font-size: 80px;
    line-height: 100px;
  }
  .contact-us-content {
    gap: 30px;
  }
  .wg-card-store .card-store-info {
    padding-left: 15px;
    padding-right: 15px;
  }
  .wg-card-store .card-store-info .card-store-heading {
    margin-bottom: 24px;
  }
  .wg-card-store .card-store-info > ul {
    gap: 20px;
  }
  .tf-store-list.style-row {
    margin-bottom: 40px;
  }
  .px-xl {
    padding-left: 15px;
    padding-right: 15px;
  }
  .pl-xl {
    padding-left: 15px;
  }
  .modal-search .modal-dialog .modal-content {
    padding: 15px;
    gap: 20px;
  }
  .modal-search .modal-dialog .modal-content .tf-grid-layout {
    padding-right: 10px;
    margin-right: -15px;
  }
  .modal-size-guide .modal-dialog {
    width: 700px;
  }
  .modal-size-guide .modal-dialog .modal-content {
    padding: 15px;
  }
  .modal-size-guide .modal-dialog .modal-content .icon-close-popup {
    width: 25px;
    height: 25px;
    font-size: 10px;
  }
  .modal-size-guide .modal-dialog .modal-content .widget-size {
    gap: 16px;
  }
  .modal-size-guide .modal-dialog .modal-content .widget-size .box-title-size .title-size {
    width: 70px;
  }
  .modal-size-guide .modal-dialog .modal-content .header {
    margin-bottom: 20px;
  }
  .tab-size {
    gap: 16px;
  }
  .tab-size .size-button-wrap {
    gap: 10px;
  }
  .tab-size .size-button-wrap .size-button-item {
    padding: 10px 0;
  }
  .logo-header {
    display: flex;
    justify-content: center;
  }
  .header-default .wrapper-header .nav-icon {
    gap: 10px;
  }
  .header-style-3 .box-navigation {
    display: none;
  }
  .header-style-3 .wrapper-header-left {
    justify-content: center;
  }
  .list-color-product .list-color-item {
    width: 20px;
    height: 20px;
  }
  .slider-scroll,
  .thumbs-slider {
    flex-direction: column !important;
  }
  .slider-scroll > div,
  .thumbs-slider > div {
    width: 100%;
  }
  .slider-scroll .tf-product-media-thumbs,
  .thumbs-slider .tf-product-media-thumbs {
    order: 1;
  }
  .slider-nav-sw .nav-sw {
    display: none;
  }
  .slider-nav-sw .wrap-pagination {
    display: block;
  }
  .tf-slideshow .nav-sw {
    display: none;
  }
  .flat-spacing-5 {
    padding: 40px 30px;
  }
}
@media only screen and (max-width: 1024px) {
  .coming-soon .coming-soon-inner .content .heading {
    margin-bottom: 16px;
  }
  .title-display,
  .title-display-2 {
    font-size: 50px;
    line-height: 58px;
  }
  h1 {
    font-size: 46px;
    line-height: 58px;
  }
  h2 {
    font-size: 34px;
    line-height: 40px;
  }
  h3 {
    font-size: 30px;
    line-height: 38px;
  }
  h4 {
    font-size: 26px;
    line-height: 36px;
  }
  h5 {
    font-size: 20px;
    line-height: 26px;
  }
  h6 {
    font-size: 18px;
    line-height: 26px;
  }
  .h6 {
    font-size: 18px !important;
    line-height: 26px !important;
  }
}
@media only screen and (max-width: 991px) {
  .tf-sticky-btn-atc {
    bottom: 67px;
  }
  .tf-toolbar-bottom {
    display: flex;
  }
  .tf-add-cart-success {
    display: none;
  }
  .modal-search .tf-loading {
    height: 42px;
  }
  .icv__label {
    padding: 8px 15px;
  }
  #image-compare {
    width: 100%;
    height: 400px;
  }
  #image-compare img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .section-flash-sale .wrap {
    gap: 30px;
  }
  .section-flash-sale .wrap .image {
    display: none;
  }
  .wg-big-save .content {
    left: 15px;
  }
  .tf-list-categories.style-1 {
    display: none;
  }
  .slider-pet-store .wrap-slider .content-slider {
    padding-left: 15px;
    padding-right: 15px;
  }
  .grid-card-product {
    gap: 20px;
  }
  .tf-shop-control .tf-control-layout {
    gap: 6px;
  }
  .wg-pagination .pagination-item {
    width: 36px;
    height: 36px;
  }
  .wrap-sidebar-account {
    display: none;
  }
  .terms-of-use-wrap {
    gap: 30px;
  }
  .terms-of-use-wrap .left {
    width: 300px;
  }
  .terms-of-use-wrap .right .heading {
    margin-bottom: 32px;
  }
  .contact-us-map {
    flex-direction: column;
  }
  .contact-us-map .right {
    padding: 30px 15px;
    max-width: unset;
  }
  .tf-product-deals {
    padding-left: 15px;
    padding-right: 15px;
  }
  .product-fixed-scroll .accordion-product-wrap {
    padding-top: 20px;
  }
  .product-fixed-price .left-desc {
    max-width: unset;
    padding-top: 40px !important;
  }
  .product-fixed-price .grid-image-top {
    gap: 16px 15px;
    margin-bottom: 40px;
  }
  .product-fixed-price .grid-image-top .item-3 {
    margin-bottom: 16px;
  }
  .tf-sticky-btn-atc .tf-sticky-atc-infos {
    gap: 15px;
  }
  .tf-sticky-btn-atc .tf-sticky-atc-infos .tf-sticky-atc-btns {
    width: 150px;
  }
  .tf-sticky-btn-atc .tf-sticky-atc-infos .wg-quantity {
    width: 120px;
  }
  .tab-shipping {
    flex-direction: column;
  }
  .tf-slideshow .wrap-slider {
    height: 500px;
  }
  .tf-slideshow .box-title-slider p br {
    display: none;
  }
  .tf-slideshow .card-box br {
    display: none;
  }
  .sw-dots.type-circle .swiper-pagination-bullet {
    width: 16px !important;
    height: 16px !important;
  }
  .sw-dots.type-circle .swiper-pagination-bullet::after {
    width: 6px;
    height: 6px;
  }
  .sw-dots.type-square .swiper-pagination-bullet {
    width: 15px;
  }
  .sw-dots.type-square .swiper-pagination-bullet.swiper-pagination-bullet-active {
    width: 30px;
  }
  .product-description-list .product-description-list-item .product-description-list-content,
  .accordion-product-wrap .accordion-product-item .accordion-content {
    padding: 30px 15px;
  }
  .widget-tabs.style-menu-tabs {
    flex-wrap: wrap;
    gap: 28px;
  }
  .widget-tabs.style-menu-tabs .widget-menu-tab {
    flex-direction: row;
    width: 100%;
  }
  .widget-tabs.style-menu-tabs .widget-menu-tab .item-title {
    padding-left: 0 !important;
    flex-shrink: 0;
  }
  .widget-tabs.style-menu-tabs .widget-menu-tab .item-title::after {
    bottom: 0px;
    left: 0;
    width: 0;
    height: 1px !important;
    top: unset;
  }
  .widget-tabs.style-menu-tabs .widget-menu-tab .item-title.active::after {
    width: 100%;
  }
  .widget-tabs.style-menu-tabs .widget-content-inner {
    padding: 30px 15px;
  }
  .widget-tabs.style-1 .widget-menu-tab {
    justify-content: start;
  }
  .widget-tabs.style-1 .widget-content-inner {
    padding: 30px 15px;
  }
  .tab-description {
    flex-wrap: wrap;
  }
  .blog-detail-wrap > .inner {
    padding: 15px 15px 0;
  }
  .wg-blog .image {
    margin-bottom: 16px;
  }
  .wg-blog.style-1 .image {
    margin-bottom: 15px;
  }
  .tf-btn,
  button {
    padding: 12px 20px;
    font-size: 14px;
    line-height: 22px;
  }
  .tf-btn .icon,
  button .icon {
    font-size: 18px;
  }
  .btn-line {
    font-size: 14px;
    line-height: 22px;
  }
  .btn-style-6 {
    padding: 8px 10px;
  }
  footer {
    padding-bottom: 67px;
  }
  footer .footer-menu {
    margin-bottom: 30px;
  }
  footer .footer-menu > div {
    width: 50%;
  }
  footer .footer-infor {
    margin-bottom: 30px;
  }
  footer .footer-body {
    padding: 40px 0;
  }
  footer.has-pb {
    padding-bottom: 165px;
  }
  .mb-lg-30 {
    margin-bottom: 30px;
  }
  .flat-spacing-8,
  .flat-spacing,
  .flat-spacing-10 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .flat-spacing-2 {
    padding-top: 45px;
    padding-bottom: 45px;
  }
  .flat-spacing-3 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .flat-spacing-4 {
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .flat-spacing-5 {
    padding: 40px 15px;
  }
  .flat-spacing-6 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .space-30 {
    padding-left: 15px;
    padding-right: 15px;
  }
  .heading-section-2,
  .heading-section {
    margin-bottom: 30px;
  }
  [data-grid=grid-4] {
    grid-template-columns: repeat(3, 1fr);
  }
  .flat-countdown-banner .banner-right {
    margin-top: 30px;
  }
  .testimonial-item .content {
    padding: 20px;
  }
  .testimonial-item .content-top {
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  .testimonial-item .box-avt {
    gap: 8px;
  }
  .banner-lookbook {
    height: 300px;
  }
  .loobook-product::before {
    content: none;
  }
  .testimonial-item.style-2 {
    padding: 15px;
  }
  .list-collection.style-lg .cls-item .text {
    font-size: 30px;
    line-height: 38px;
  }
  #scroll-top {
    width: 40px;
    height: 40px;
  }
  #scroll-top svg {
    width: 20px;
    height: 20px;
  }
  .tf-product-info-list .tf-countdown .countdown__timer {
    gap: 30px;
  }
  .tf-product-info-list .tf-countdown .countdown__item {
    width: 40px;
  }
  .tf-product-info-list .tf-countdown .countdown__item:not(:last-child)::after {
    right: -16px;
  }
}
@media only screen and (max-width: 767px) {
  .tf-buyx-gety-item {
    padding: 14px;
    gap: 15px;
  }
  .tf-buyx-gety-item .tags {
    top: 15px;
    right: 15px;
  }
  .tf-icon-box.style-2.type-column {
    flex-direction: column;
    gap: 12px;
    align-items: center;
    text-align: center;
  }
  .wg-library {
    flex-direction: column;
  }
  .wg-library .library-info {
    width: 100%;
    text-align: center;
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .wg-library .library-img {
    width: 100%;
  }
  .collection-default.style-row {
    grid-template-columns: 1fr;
  }
  .collection-default.style-row .content {
    padding: 30px 15px;
  }
  .collection-default.style-1 .img-style {
    border-radius: 8px 8px 0 0;
    aspect-ratio: 1.7755681818;
  }
  .collection-default.style-1 .content {
    padding: 30px 15px;
    border-left: 1px solid var(--line);
    border-top: 0;
    border-radius: 0 0 8px 8px;
    text-align: center;
  }
  .collection-default.abs-top-2 .image img {
    min-height: 300px;
    object-fit: cover;
  }
  .banner-text.style-2 {
    padding: 30px 15px;
  }
  footer.has-pb {
    padding-bottom: 197px;
  }
  .loobook-product:not(.style-row) .img-style {
    height: 120px;
  }
  .tab-product .nav-tab-item a {
    font-size: 18px;
    line-height: 30px;
  }
  .md-overflow-x {
    overflow-x: scroll;
    flex-wrap: nowrap;
    justify-content: start;
  }
  .footer .footer-heading-mobile {
    display: block;
    position: relative;
  }
  .footer .footer-heading-mobile::after {
    position: absolute;
    content: "";
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 1px;
    background-color: var(--main);
    transition: 0.25s ease-in-out;
  }
  .footer .footer-heading-mobile::before {
    position: absolute;
    content: "";
    right: 15px;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 12px;
    background-color: var(--main);
    transition: 0.25s ease-in-out;
  }
  .footer .footer-col-block.open .footer-heading-mobile::before {
    opacity: 0;
  }
  .footer .footer-col-block.open .footer-heading-mobile::after {
    transform: translate(0%, -50%) rotate(180deg);
  }
  .footer .footer-col-block .tf-collapse-content {
    display: none;
  }
  .footer .footer-menu {
    flex-direction: column;
    gap: 18px;
    margin-bottom: 18px;
  }
  .footer .footer-menu > div {
    width: 100%;
  }
  .section-flash-sale .wrap {
    flex-direction: column;
    padding: 30px 0;
  }
  .section-flash-sale .wrap > div {
    width: 100%;
    text-align: center !important;
  }
  .section-flash-sale .wrap p {
    margin-bottom: 16px !important;
  }
  .tf-compare-row:not(:first-child) .tf-compare-col:nth-child(2) {
    border-left: 1px solid var(--line);
  }
  .wg-pet-store .pet-store-heading {
    padding: 8px 15px;
  }
  .wg-pet-store .pet-store-list .pet-store-item {
    padding: 8px 15px;
  }
  .wg-benefit .benefit-item {
    justify-content: center;
  }
  .collection-position-2.style-3 .cls-btn h6 {
    flex-grow: 1;
  }
  .collection-position-2.style-3 .cls-btn .count-item {
    display: none;
  }
  .collection-position.style-1 .content {
    bottom: 24px;
    left: 15px;
  }
  .wg-free-delivery .free-delivery-info {
    padding-left: 20px;
    padding-right: 20px;
  }
  .tf-countdown.style-2 .countdown__timer {
    gap: 30px;
  }
  .tf-countdown.style-2 .countdown__timer .countdown__item {
    width: 50px;
    height: 50px;
  }
  .tf-countdown.style-2 .countdown__timer .countdown__item .countdown__value {
    font-size: 24px;
    line-height: 32px;
  }
  .tf-countdown.style-2 .countdown__timer .countdown__item .countdown__label {
    font-size: 12px;
    line-height: 20px;
    left: 42px;
  }
  .tf-control-layout .sw-layout-3 {
    display: none;
  }
  .wg-blog.style-row {
    flex-direction: column;
  }
  .wg-blog.style-row > * {
    width: 100% !important;
  }
  .wg-blog.style-row .content {
    padding: 16px 0 0 0;
    gap: 12px;
  }
  .login-wrap {
    flex-direction: column;
    gap: 40px;
  }
  .login-wrap::before {
    display: none;
  }
  .terms-of-use-wrap {
    flex-direction: column;
  }
  .terms-of-use-wrap .left {
    width: 100%;
    position: unset;
  }
  .terms-of-use-wrap .right .heading {
    margin-bottom: 20px;
  }
  .page-faqs-wrap {
    flex-direction: column;
  }
  .page-faqs-wrap .ask-question {
    width: unset;
    padding: 24px 15px;
  }
  .page-404 .page-404-inner {
    flex-direction: column;
  }
  .page-404 .page-404-inner .content {
    gap: 16px;
    text-align: center;
  }
  .page-404 .page-404-inner .content .heading {
    font-size: 36px;
    line-height: 48px;
  }
  .page-404 .page-404-inner .content a {
    margin-left: auto;
    margin-right: auto;
  }
  .contact-us-content {
    flex-direction: column;
  }
  .contact-us-content .right {
    max-width: unset;
  }
  .contact-us-content .right h4 {
    margin-bottom: 20px;
  }
  .tf-store-list {
    margin-bottom: 40px;
  }
  .tf-store-list .tf-store-item {
    padding: 20px 15px;
  }
  .map-contact {
    height: 500px;
  }
  .team-item .tf-social-icon a {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  .form-bundle-product {
    padding: 24px 15px;
  }
  .form-bundle-product .tf-bundle-product-item {
    gap: 10px;
  }
  .form-bundle-product .tf-bundle-product-item .tf-check {
    margin: 0;
  }
  .reply-comment.style-1 .type-reply {
    margin-left: 15px;
  }
  .modal-wishlist .modal-content {
    max-width: min(500px, 90vw) !important;
  }
  .modal-wishlist .modal-content .tf-mini-cart-bottom,
  .modal-wishlist .modal-content .header {
    padding: 20px 15px;
  }
  .modal-wishlist .modal-content .tf-mini-cart-item {
    margin-left: 15px;
    margin-right: 15px;
  }
  .modal-shopping-cart .modal-content {
    max-width: min(500px, 90vw) !important;
  }
  .modal-quick-view .modal-content {
    max-width: min(500px, 90vw) !important;
    flex-direction: column;
    overflow-y: auto !important;
  }
  .modal-quick-view .modal-content .tf-quick-view-image {
    width: 100%;
    display: block;
  }
  .modal-quick-view .modal-content .tf-quick-view-image .wrap-quick-view {
    padding: 15px;
    display: flex;
    direction: ltr;
    height: unset;
  }
  .modal-quick-view .modal-content .tf-quick-view-image .wrap-quick-view .quickView-item {
    width: 200px;
    flex-shrink: 0;
    border-radius: 0;
  }
  .modal-quick-view .modal-content .tf-quick-view-image .wrap-quick-view .quickView-item:not(:last-child) {
    margin-bottom: 0;
    margin-right: 15px;
  }
  .modal-quick-view .modal-content .wrap {
    overflow: unset;
    height: unset;
    padding: 20px 15px;
  }
  .form-sticky-atc .tf-sticky-atc-product {
    display: none;
  }
  .form-sticky-atc .tf-sticky-atc-infos {
    width: 100%;
    gap: 10px;
  }
  .form-sticky-atc .tf-sticky-atc-infos .tf-sticky-atc-infos-title {
    display: none;
  }
  .form-sticky-atc .tf-sticky-atc-infos .tf-sticky-atc-btns {
    width: calc(100% - 135px);
  }
  .offcanvas-compare .tf-compare-list {
    flex-direction: column;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-head br {
    display: none;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-wrap {
    margin: 0;
    padding: 15px 0;
    width: 100%;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item {
    flex-direction: column;
    width: 150px;
    gap: 7px;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item .image {
    width: unset;
    height: unset;
    max-height: 168px;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-buttons {
    width: 100%;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-buttons .tf-compare-buttons-wrap {
    display: flex;
    gap: 10px;
    width: 100%;
    flex-direction: column;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-buttons .tf-compare-buttons-wrap a {
    font-size: 12px;
    height: 40px;
  }
  .offcanvas-compare .tf-compare-list .tf-compare-buttons .tf-compare-buttons-wrap .tf-compapre-button-clear-all {
    margin: 0;
    height: 40px;
    font-size: 12px;
  }
  .modal-size-guide .modal-dialog {
    width: 550px;
  }
  .modal-shopping-cart .modal-content {
    flex-direction: column;
  }
  .modal-shopping-cart .list-cart {
    display: flex;
    gap: 15px;
    overflow-x: auto;
  }
  .modal-shopping-cart .list-cart .list-cart-item {
    padding-bottom: 0 !important;
    border-bottom: 0 !important;
    margin-bottom: 0 !important;
    min-width: 120px;
  }
  .modal-shopping-cart .list-cart .list-cart-item .image {
    max-height: 150px;
  }
  .modal-shopping-cart .tf-minicart-recommendations {
    padding: 15px 15px 0 15px;
    width: unset;
  }
  .modal-shopping-cart .header {
    padding: 12px;
  }
  .modal-shopping-cart .tf-mini-cart-threshold {
    margin: 0 12px;
    padding: 12px;
  }
  .modal-shopping-cart .tf-mini-cart-item {
    margin: 0 12px;
    padding: 12px 0;
    gap: 12px;
  }
  .modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-info > div {
    gap: 6px;
    margin-bottom: 6px;
  }
  .modal-shopping-cart .tf-mini-cart-tool {
    padding: 0 12px;
    height: 40px;
  }
  .modal-shopping-cart .tf-mini-cart-tool .tf-mini-cart-tool-btn {
    gap: 6px;
  }
  .modal-shopping-cart .tf-mini-cart-tool .tf-mini-cart-tool-btn div {
    font-size: 10px;
    line-height: 20px;
  }
  .modal-shopping-cart .tf-mini-cart-bottom-wrap {
    padding: 12px;
  }
  .modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-checkbox {
    margin-bottom: 12px;
  }
  .modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-checkbox label {
    font-size: 13px;
    line-height: 20px;
  }
  .modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-mini-cart-view-checkout {
    gap: 10px;
    margin-bottom: 12px;
  }
  .modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-mini-cart-view-checkout .tf-btn {
    padding: 8px 14px;
  }
  .modal-shopping-cart .tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-mini-cart-tool-wrap,
  .modal-shopping-cart .tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-mini-cart-tool-text {
    padding: 12px;
  }
  .tf-main-product.full-width {
    flex-direction: column;
  }
  .tf-main-product.full-width > div {
    width: 100%;
  }
  .tf-slideshow .wrap-slider {
    height: 400px;
  }
  .tf-slideshow .wrap-slider.h-600 {
    height: 600px;
  }
  .tf-slideshow .title-display,
  .tf-slideshow .title-display-2 {
    font-size: 30px;
    line-height: 42px;
  }
  .wrap-slider .box-title-slider {
    gap: 4px;
  }
  .wrap-slider .content-slider {
    gap: 12px;
  }
  .slider-effect {
    display: flex;
    flex-direction: column-reverse;
  }
  .slider-effect .img-slider {
    width: 100%;
    height: 300px;
  }
  .slider-effect .content-left {
    position: unset;
    padding: 40px 0px;
    text-align: center;
  }
  .tab-reviews .tab-reviews-heading {
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
  }
  .tf-product-info-wrap {
    padding-top: 30px;
  }
  .title-display,
  .title-display-2 {
    font-size: 40px;
    line-height: 48px;
  }
  h1 {
    font-size: 36px;
    line-height: 48px;
  }
  h2 {
    font-size: 30px;
    line-height: 36px;
  }
  h3 {
    font-size: 28px;
    line-height: 36px;
  }
  h4 {
    font-size: 24px;
    line-height: 34px;
  }
  h5 {
    font-size: 18px;
    line-height: 24px;
  }
  h6 {
    font-size: 16px;
    line-height: 24px;
  }
  .h6 {
    font-size: 16px !important;
    line-height: 24px !important;
  }
  #header .nav-icon .nav-account,
  #header .nav-icon .nav-wishlist,
  #header .nav-icon .nav-compare {
    display: none;
  }
  [data-grid=grid-4] {
    grid-template-columns: repeat(2, 1fr);
  }
  .card-product .countdown-box,
  .card-product .size-list,
  .card-product .wishlist,
  .card-product .compare {
    display: none;
  }
  .flat-countdown-banner .banner-img {
    display: none;
  }
  .flat-img-with-text .banner-right {
    height: 400px;
  }
  .flat-img-with-text .banner-left {
    height: 400px;
  }
  .flat-img-with-text-v3 .banner-left {
    padding-bottom: 40px;
  }
  .flat-with-text-lookbook .lookbook-content {
    margin-top: 30px;
  }
  .new-item {
    flex-direction: column;
  }
  .new-item .img-style {
    max-width: 100%;
  }
  .grid-cls-v2 .banner-cls .img-style,
  .grid-cls-v2 .collection-position-2 .img-style {
    height: 400px;
  }
  .banner-cls-discover .img-style {
    height: 400px;
  }
  .tf-table-page-cart thead {
    display: none;
  }
  .tf-table-page-cart .tf-cart-item {
    padding-left: 98px;
    min-height: 140px;
    display: block;
    position: relative;
  }
  .tf-table-page-cart .tf-cart-item:not(:last-child) {
    margin-bottom: 15px;
  }
  .tf-table-page-cart .tf-cart-item td {
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 10px 0;
    margin: 0;
  }
  .tf-table-page-cart .tf-cart-item td:not(:last-child) {
    border-bottom: 1px dashed var(--line);
  }
  .tf-table-page-cart .tf-cart-item .img-box {
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
    width: 80px;
    max-height: 110px;
  }
  .tf-table-page-cart .tf-cart-item td[data-cart-title]:before {
    content: attr(data-cart-title);
    color: var(--main);
    text-align: start;
    flex: 1 1 auto;
  }
  .ip-discount-code .tf-btn {
    padding: 6px 10px;
  }
  .ip-discount-code input {
    padding: 12px 15px;
    padding-right: 120px;
  }
  .heading-section h3,
  .heading-section-2 h3 {
    font-size: 24px;
    line-height: 30px;
  }
  .heading-section h3 {
    margin-bottom: 8px;
  }
  .heading-section-2 {
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  .sw-dots {
    gap: 4px;
  }
  .blog-detail-wrap > .image,
  .flat-banner-parallax-v3,
  .flat-banner-parallax-v2 {
    background-attachment: scroll;
  }
}
@media only screen and (max-width: 575px) {
  .news-item-2 .image {
    width: 100%;
  }
  .news-item-2 .content {
    width: 100%;
  }
  .tf-icon-box.style-3 {
    border: 0 !important;
  }
  .collection-default.abs-left-bottom.type-xl .img-style img {
    min-height: 300px;
  }
  .news-item-2 {
    flex-wrap: wrap;
    border-radius: 16px;
  }
  .news-item-2 .image {
    max-width: 100%;
  }
  .news-item-2 .content {
    height: unset;
    padding: 0px 15px 20px;
  }
  .form-sticky-atc .tf-sticky-atc-infos {
    flex-wrap: wrap;
    justify-content: center;
  }
  .form-sticky-atc .tf-sticky-atc-infos .tf-sticky-atc-btns {
    width: 100%;
  }
  .form-sticky-atc .tf-sticky-atc-infos .tf-dropdown-sort {
    min-width: 100px;
  }
  .tf-topbar .topbar-left {
    flex-grow: 1;
    justify-content: space-between;
    overflow: auto;
  }
  .tf-topbar .topbar-left a {
    white-space: nowrap;
  }
  .section-newsletter form input {
    padding-right: 126px;
  }
  .section-newsletter form button {
    padding: 12px 16px;
  }
  .coming-soon .coming-soon-inner .content {
    max-width: unset;
  }
  .coming-soon .coming-soon-inner .content .countdown__timer {
    gap: 32px;
  }
  .coming-soon .coming-soon-inner .content .countdown__item::after {
    right: -25%;
  }
  .coming-soon .coming-soon-inner .content .countdown__value {
    font-size: 20px;
  }
  .wg-card-store .card-store-info > ul {
    grid-template-columns: 1fr;
  }
  .product-fixed-price .grid-image-top {
    flex-direction: column;
  }
  .tf-product-modal .modal-dialog {
    max-width: calc(100vw - 30px);
    margin-left: 15px;
    margin-right: 15px;
    align-items: flex-end;
  }
  .tf-product-modal .modal-dialog .modal-content {
    padding: 25px 15px;
  }
  .modal-size-guide .modal-dialog {
    width: unset;
    margin-left: 15px;
    margin-right: 15px;
  }
  .card-product .description {
    display: none;
  }
  .testimonial-item {
    flex-direction: column;
  }
  .testimonial-item .img-style {
    max-width: 100%;
    height: 273px;
  }
  .tf-page-checkout .grid-2 {
    grid-template-columns: 1fr;
  }
}
/*-------------- RTL ----------------- */
#toggle-rtl {
  padding: 0;
  position: fixed;
  top: 50%;
  right: 15px;
  width: 40px;
  height: 40px;
  min-width: 30px;
  text-transform: uppercase;
  z-index: 999;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rtl .testimonial-item-2 .content-top {
  text-align: right;
}
.rtl .testimonial-item-2 .list-star-default,
.rtl .testimonial-item-2 .box-author {
  justify-content: flex-end;
  text-align: right;
}
.rtl .collection-position-2.style-5.has-overlay .content {
  text-align: right !important;
}
.rtl .collection-default.abs-left-bottom .content {
  left: unset;
  right: 20px;
  text-align: right !important;
}
.rtl .collection-default.abs-left-center.type-xl .content {
  right: 20px;
  text-align: right !important;
}
.rtl .header-style-7 .form-search button {
  width: max-content;
  right: unset;
  left: 0;
}
.rtl .sub-categories2 {
  position: relative;
}
.rtl .sub-categories2 .list-categories-inner {
  right: 101%;
  left: unset;
}
.rtl .header-style-6 .header-right .form-search button {
  width: max-content;
  right: unset;
  left: 0;
}
.rtl .tf-sw-testimonial {
  direction: ltr;
}
.rtl .offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item > .icon-close {
  left: 0;
  right: unset;
  transform: translate(-50%, -50%);
}
.rtl .tf-list-categories.style-1 > .list-categories-inner .icon {
  margin-left: 12px;
  margin-right: 0;
}
.rtl .tf-list-categories.style-1 > .list-categories-inner .list-categories-inner {
  right: 100%;
  left: unset;
}
.rtl .section-newsletter form input {
  padding-right: 12px;
}
.rtl .section-newsletter form button {
  left: 10px;
  right: unset;
}
.rtl .flat-countdown-banner-2 {
  transform: rotateY(180deg);
}
.rtl .flat-countdown-banner-2 .box-content {
  transform: rotateY(180deg);
}
.rtl .coming-soon {
  transform: rotateY(180deg);
}
.rtl .coming-soon .coming-soon-inner {
  transform: rotateY(180deg);
}
.rtl .collection-position-3 .archive-btn,
.rtl .collection-position-3 .archive-top {
  right: 15px;
  left: unset;
}
.rtl .flat-single-home .tf-product-info-list .tf-product-info-price .price-on-sale {
  margin-right: 0;
  margin-left: 24px;
}
.rtl .flat-single-home .tf-product-info-list .tf-product-info-price .old-price {
  margin-left: 12px;
  margin-right: 0;
}
.rtl .flat-single-home .tf-product-info-list .tf-product-info-price .old-price::before {
  right: -13px;
  left: unset;
}
.rtl .header-style-3 .box-navigation {
  padding-right: 60px;
  padding-left: 0;
}
.rtl .header-style-5 .form-search-select {
  margin-left: 0px;
  margin-right: 60px;
}
.rtl .header-style-5 .form-search-select .tf-dropdown-sort .dropdown-menu {
  transform: translate(444px, 44px) !important;
}
.rtl .header-style-5 .tf-list-categories .categories-title {
  padding-right: 0px;
  border-right: 0;
  padding-left: 40px;
  border-left: 1px solid rgba(233, 233, 233, 0.1);
}
.rtl .header-style-5 .tf-list-categories .list-categories-inner {
  left: unset;
  right: 0;
}
.rtl .header-style-5 .header-bottom .box-nav-ul {
  margin-left: 0;
  margin-right: 40px;
}
.rtl .tf-countdown-lg .countdown__item:not(:last-child)::after {
  left: -22px;
  right: unset;
}
.rtl .wg-pet-store,
.rtl .new-item,
.rtl .collection-social,
.rtl .testimonial-item-v2,
.rtl .collection-position-3,
.rtl .loobook-product,
.rtl .wg-blog.style-1,
.rtl .tf-icon-box.style-2,
.rtl .collection-social.style-2 {
  direction: rtl;
}
.rtl .collection-position-2 {
  direction: rtl;
}
.rtl .collection-position-2 .cls-content .price .old-price {
  margin-left: 8px;
  margin-right: 0;
}
.rtl .terms-of-use-wrap > .left h6 {
  padding-right: 16px;
  padding-left: 0;
}
.rtl .terms-of-use-wrap > .left h6::before {
  right: -1px;
  left: unset;
}
.rtl .tf-product-customer-note .tf-product-image-upload input {
  left: 8px;
  right: unset;
}
.rtl .tf-product-with-discount .tf-product-discount-list .tf-product-discount-item svg {
  transform: rotateY(180deg);
}
.rtl .tf-product-with-discount .tf-product-discount-list .tf-product-discount-item .tf-btn-discount {
  left: 6px;
  right: unset;
}
.rtl .tf-product-with-discount .tf-product-discount-list .tf-product-discount-item .tf-number-discount {
  left: 59px;
  right: unset;
}
.rtl .tf-countdown.style-1 .countdown__item:not(:last-child)::after {
  left: -16px;
  right: unset;
}
.rtl .form-has-password .toggle-password {
  left: 16px;
  right: unset;
}
.rtl .tf-cart-item .tf-cart-item_price .old-price {
  margin-left: 8px;
  margin-right: 0;
}
.rtl .tf-cart-sold .notification-progress .progress-cart .round {
  left: unset;
  right: 100%;
}
.rtl .collection-position-2.style-3 .cls-btn .icon,
.rtl .collection-position-2.style-2 .cls-btn .icon {
  left: 28px;
  right: unset;
}
.rtl .tf-dropdown-sort .select-item {
  text-align: start;
}
.rtl .widget-facet.facet-price .price-val::after {
  left: 12px;
  right: unset;
}
.rtl .meta-filter-shop .count-text {
  padding-left: 12px;
  padding-right: 0;
}
.rtl .meta-filter-shop .count-text::after {
  left: 0;
  right: unset;
}
.rtl .tf-compare-row:first-child .tf-compare-col:nth-child(2) {
  border-top-right-radius: 8px;
  border-top-left-radius: 0px;
}
.rtl .tf-compare-row:first-child .tf-compare-col:last-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 0px;
  border-left: 1px solid var(--line);
}
.rtl .tf-compare-row:not(:first-child) .tf-compare-col {
  border-left: 1px solid var(--line);
}
.rtl .tf-compare-row:not(:first-child) .tf-compare-col:last-child {
  border-right: 0;
}
.rtl .tf-compare-row:last-child .tf-compare-col:last-child {
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 8px;
}
.rtl .tf-compare-row .tf-compare-col:nth-child(2) {
  border-left: 0;
}
.rtl .payment-box .payment-item .payment-body .ip-card .list-card {
  left: 16px;
  right: unset;
}
.rtl .ip-discount-code input {
  padding-right: 20px;
  padding-left: 140px;
}
.rtl .ip-discount-code .tf-btn {
  left: 8px;
  right: unset;
}
.rtl .meta .meta-item:not(:last-child)::after {
  left: -17px;
  right: unset;
}
.rtl .team-item .content {
  flex-direction: row-reverse;
}
.rtl .team-item .content .name,
.rtl .team-item .content .infor {
  text-align: end;
}
.rtl .card-product:not(.style-2, .style-6, .style-7, .style-list) .list-product-btn {
  left: 5px;
  right: unset;
}
.rtl .card-product:not(.style-2, .style-6, .style-7, .style-list) .box-icon .tooltip {
  left: 100%;
  right: unset;
}
.rtl .card-product:not(.style-2, .style-6, .style-7, .style-list) .box-icon:hover .tooltip {
  transform: translateX(8px);
}
.rtl .card-product:not(.style-2, .style-6, .style-7, .style-list) .tooltip::before {
  left: -4px;
  right: unset;
}
.rtl .card-product .card-product-info .old-price {
  margin-left: 8px;
  margin-right: 0;
}
.rtl .card-product .marquee-product .marquee-wrapper {
  animation-name: infiniteScrollRv;
}
.rtl .card-product.style-7 .list-product-btn .box-icon:not(:last-child) {
  border-left: 1px solid var(--line);
  border-right: 0;
}
.rtl .tf-marquee:not(.marquee-animation-right) .marquee-wrapper {
  animation-name: infiniteScrollRv;
}
.rtl .tf-marquee.marquee-animation-right .marquee-wrapper {
  direction: ltr;
  animation-name: infiniteScroll;
}
.rtl .box-nav-ul .menu-list li {
  text-align: start;
}
.rtl .tf-has-purchased .icon-close {
  left: 12px;
  right: unset;
}
.rtl .nav-account .dropdown-account {
  left: -50px;
  right: unset;
}
.rtl .tf-product-info-price .compare-at-price {
  margin-left: 16px;
  margin-right: 0;
}
.rtl .tf-product-info-price .price-on-sale {
  margin-left: 8px;
  margin-right: 0;
}
.rtl .tf-product-info-price.type-1 .compare-at-price::before {
  right: -5px;
  left: unset;
}
.rtl .tf-product-info-list .tf-product-info-help .tf-product-info-extra-link .tf-product-extra-icon:not(:last-child)::after {
  left: -18px;
  right: unset;
}
.rtl .dropdown.dropdown-store-location .dropdown-content h6,
.rtl .dropdown.dropdown-store-location .dropdown-content p {
  text-align: start;
}
.rtl .modal-size-guide .modal-content .header .icon-close-popup {
  left: 20px;
  right: unset;
}
.rtl .tab-reviews .rating-score .icon {
  margin-right: 4px;
  margin-left: 0;
}
.rtl .write-cancel-review-wrap.write-review .check-save label {
  margin-right: 8px;
  margin-left: 0;
}
.rtl .widget-tabs.style-menu-tabs .widget-menu-tab .item-title.active {
  padding-right: 16px;
  padding-left: 0;
}
.rtl .widget-tabs.style-menu-tabs .widget-menu-tab .item-title::after {
  left: unset;
  right: 0;
}
.rtl .tf-product-modal .form-share input {
  padding-right: 16px;
  padding-left: 80px;
}
.rtl .tf-product-modal .form-share .button-submit {
  left: 0;
  right: unset;
}
.rtl .offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item:last-child > .btns-repeat {
  display: block;
}
.rtl .offcanvas-compare .tf-compare-list .tf-compare-wrap .tf-compare-item:first-child > .btns-repeat {
  display: none;
}
.rtl .modal-shopping-cart .tf-minicart-recommendations {
  border-left: 1px solid var(--line);
  border-right: 0;
}
.rtl .modal-shopping-cart .tf-mini-cart-threshold .tf-progress-bar .icon {
  left: -5%;
}
.rtl .tf-select::after {
  left: 16px;
  right: unset !important;
}
.rtl #toggle-rtl {
  left: 15px;
  right: auto;
}
.rtl .canvas-mb .mb-bottom .bottom-bar-language .tf-currencies {
  border-left: 1px solid var(--line);
  border-right: 0;
}
.rtl .noUi-horizontal .noUi-handle {
  left: auto;
  right: -8px;
}
.rtl .widget-facet {
  overflow: hidden;
}
.rtl #scroll-top {
  left: 20px;
  right: unset;
}
.rtl .card-product {
  direction: rtl;
}
.rtl .tf-sw-slideshow .wrap-slider {
  direction: rtl;
}
.rtl .tf-sw-slideshow .wrap-slider img {
  transform: rotateY(180deg);
}
.rtl .slider-effect .img-slider {
  margin-left: unset;
  margin-right: auto;
}
.rtl .slider-effect .content-left {
  direction: rtl;
}
.rtl .slider-radius .row-end .box-content {
  left: 0;
}
.rtl .slider-radius .row-end img {
  transform: unset;
}
.rtl .tf-breadcrumb-wrap .tf-breadcrumb-prev-next,
.rtl .box-nav-pagination {
  direction: ltr;
}
.rtl .wrap-header-left .box-navigation {
  padding-left: 0;
  padding-right: 40px;
}
.rtl .tf-slideshow .grid-img-group .item-2 {
  margin-left: unset;
  margin-right: auto;
}
.rtl .tf-currencies {
  direction: ltr;
}
.rtl .tf-currencies .dropdown-menu .text {
  justify-content: flex-end !important;
}
.rtl .tf-currencies .dropdown-menu::after {
  display: none !important;
}
.rtl .testimonial-item {
  direction: rtl;
}
.rtl .testimonial-item .product > a {
  right: auto;
  left: 0;
}
.rtl .footer .footer-heading-mobile::before {
  left: 15px;
  right: auto;
}
.rtl .footer .footer-heading-mobile::after {
  left: 10px;
  right: auto;
}
.rtl .footer .footer-newsletter input {
  padding-left: 143px;
  padding-right: 18px;
}
.rtl .footer .footer-newsletter .button-submit button {
  right: auto;
  left: 8px;
}
.rtl .modal.fullRight .modal-dialog {
  transform: translate(-100%, 0) !important;
  transition: all 0.3s !important;
}
.rtl .modal.fullRight .modal-dialog .modal-content {
  left: 0;
  right: unset;
}
.rtl .modal.fullRight.show .modal-dialog {
  transform: translate(0, 0) !important;
}
.rtl .offcanvas.offcanvas-end {
  left: 0;
  right: unset;
  transform: translateX(-100%);
}
.rtl .offcanvas.offcanvas-start {
  right: 0;
  left: unset;
  transform: translateX(100%);
}
.rtl .offcanvas.show:not(.hiding),
.rtl .offcanvas.showing {
  transform: none;
}
.rtl .offcanvas.hiding,
.rtl .offcanvas.show,
.rtl .offcanvas.showing {
  visibility: visible;
}
.rtl .card-product .on-sale-item,
.rtl .icv__theme-wrapper {
  direction: ltr;
}
.rtl .drift-zoom-pane.drift-inline {
  display: flex;
  justify-content: flex-end;
}
.rtl .tf-zoom-main .drift-zoom-pane {
  display: flex;
  justify-content: flex-end;
}
.rtl .box-nav-ul .menu-item-2::after {
  left: 0;
  right: unset;
  content: "\e905";
}
.rtl .box-nav-ul .menu-item-2 .sub-menu {
  right: calc(100% + 23px);
  left: unset;
}
.rtl .box-nav-ul .menu-item-2 .sub-menu::after {
  left: 90%;
}
.rtl .tf-marquee .wrap-marquee {
  animation: slide-har-reverse 6s linear infinite;
}
.rtl .box-sw-announcement-bar {
  animation: slide-har-reverse 4s linear infinite;
}
.rtl .speed-1 {
  animation: slide-har-reverse 15s linear infinite !important;
}
.rtl .tf-bundle-product-item {
  direction: rtl;
}
.rtl .footer.has-border .footer-col.footer-col-1 {
  padding-right: 90px;
  padding-left: 30px;
}
.rtl .footer.has-border .footer-col.footer-col-2 {
  padding-right: 0px;
  padding-left: 30px;
}
.rtl .footer.has-border .footer-col.footer-col-3 {
  padding-right: 0px;
  padding-left: 30px;
}
.rtl .footer.has-border .footer-newsletter {
  padding-left: 123px;
  padding-right: 0;
  border-right: 0;
  border-left: 1px solid var(--line);
}
@keyframes slide-har-reverse {
  0% {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
  }
  100% {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
  }
}
.rtl .tf-cart-item .tf-cart-item_product .img-box {
  margin-left: 24px;
  margin-right: 0px;
}
.rtl .sib-form .form-newsletter button {
  left: 4px;
  right: unset;
}

@media (min-width: 576px) {
  .rtl .tf-sw-testimonial .box-navigation.style-2 {
    left: 0px;
    right: unset;
  }
}
@media (min-width: 768px) {
  .rtl .wg-blog.style-row .content {
    padding: 20px 40px 20px 0px;
  }
  .rtl .modal-quick-view .tf-quick-view-image > .wrap-quick-view {
    padding-right: 24px;
    padding-left: 0;
    direction: ltr;
  }
  .rtl .tf-marquee .wrap-marquee {
    animation: slide-har-reverse 10s linear infinite;
  }
  .rtl .box-sw-announcement-bar {
    animation: slide-har-reverse 14s linear infinite;
  }
  .rtl .speed-1 {
    animation: slide-har-reverse 40s linear infinite !important;
  }
  .rtl .s-testimonial .content-left {
    border-left: 1px solid var(--line);
    border-right: unset;
    padding-left: 30px;
    padding-right: 0px;
  }
}
@media (min-width: 992px) {
  .rtl .collection-position-3 .archive-btn,
  .rtl .collection-position-3 .archive-top {
    right: 40px;
    left: unset;
  }
  .rtl .tf-countdown.style-1 .countdown__item:not(:last-child)::after {
    left: -16px;
    right: unset;
  }
  .rtl .image-select.type-currencies > .dropdown-menu,
  .rtl .image-select.type-languages > .dropdown-menu {
    margin-left: -15px !important;
  }
}
@media (min-width: 1200px) {
  .rtl .tf-countdown-lg .countdown__item:not(:last-child)::after {
    left: -27px;
    right: unset;
  }
  .rtl .collection-default.abs-left-bottom .content {
    right: 40px;
  }
  .rtl .collection-default.abs-left-center.type-xl .content {
    right: 80px;
  }
  .rtl .s-testimonial .content-left {
    padding-left: 60px;
  }
}
@media only screen and (max-width: 991px) {
  .rtl .flat-countdown-banner .banner-img {
    left: 0;
    right: unset;
  }
}
@media only screen and (max-width: 767px) {
  .rtl .tf-cart-item .img-box {
    left: auto;
    right: 0;
  }
}
@keyframes infiniteScrollRv {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/*# sourceMappingURL=styles.css.map */
